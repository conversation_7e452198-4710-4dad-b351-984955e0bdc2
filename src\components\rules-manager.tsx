'use client';

import { useState, useMemo } from 'react';
import { EnhancementRule, RuleCategory } from '@/types';
import { ruleCategories, defaultRules, getRulesByCategory, searchRules } from '@/lib/default-rules';
import { Button } from '@/components/ui/button';
import { Search, Plus, Settings, Play, Pause, Edit, Trash2, Copy, Download, Upload } from 'lucide-react';

interface RulesManagerProps {
  onCreateRule: () => void;
  onEditRule: (rule: EnhancementRule) => void;
  onTestRule: (rule: EnhancementRule) => void;
  onClose: () => void;
}

export function RulesManager({ onCreateRule, onEditRule, onTestRule, onClose }: RulesManagerProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [rules, setRules] = useState<EnhancementRule[]>(defaultRules);

  const filteredRules = useMemo(() => {
    let filteredRules = rules;
    
    if (selectedCategory !== 'all') {
      filteredRules = getRulesByCategory(selectedCategory);
    }
    
    if (searchQuery.trim()) {
      filteredRules = searchRules(searchQuery);
    }
    
    return filteredRules;
  }, [selectedCategory, searchQuery, rules]);

  const handleToggleRule = (ruleId: string) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
    ));
  };

  const handleDeleteRule = (ruleId: string) => {
    if (confirm('Are you sure you want to delete this rule?')) {
      setRules(prev => prev.filter(rule => rule.id !== ruleId));
    }
  };

  const handleDuplicateRule = (rule: EnhancementRule) => {
    const duplicatedRule: EnhancementRule = {
      ...rule,
      id: `${rule.id}-copy-${Date.now()}`,
      name: `${rule.name} (Copy)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setRules(prev => [...prev, duplicatedRule]);
  };

  const handleExportRules = () => {
    const exportData = {
      rules: filteredRules,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `queryboost-rules-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getPriorityColor = (priority: number) => {
    if (priority >= 8) return 'text-red-600 bg-red-100';
    if (priority >= 6) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-full sm:h-[90vh] flex flex-col lg:flex-row overflow-hidden">
        {/* Sidebar */}
        <div className="w-full lg:w-1/4 bg-gray-50 border-b lg:border-b-0 lg:border-r border-gray-200 p-4 lg:overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">Enhancement Rules</h2>
            <Button variant="ghost" onClick={onClose} className="p-2 min-h-[44px] min-w-[44px]">
              ×
            </Button>
          </div>
          
          {/* Actions */}
          <div className="space-y-2 mb-4">
            <Button onClick={onCreateRule} className="w-full flex items-center justify-center gap-2 min-h-[44px]">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Create New Rule</span>
              <span className="sm:hidden">New Rule</span>
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleExportRules} className="flex-1 min-h-[40px]">
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline ml-1">Export</span>
              </Button>
              <Button variant="outline" size="sm" className="flex-1 min-h-[40px]">
                <Upload className="h-4 w-4" />
                <span className="hidden sm:inline ml-1">Import</span>
              </Button>
            </div>
          </div>

          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search rules..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Categories */}
          <div className="space-y-1">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                selectedCategory === 'all' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <span>📋</span>
                <span>All Rules</span>
                <span className="ml-auto text-sm text-gray-500">
                  {rules.length}
                </span>
              </div>
            </button>
            
            {ruleCategories.map((category) => {
              const count = getRulesByCategory(category.id).length;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    selectedCategory === category.id 
                      ? 'bg-blue-100 text-blue-700' 
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <span>{category.icon}</span>
                    <span className="text-sm">{category.name}</span>
                    <span className="ml-auto text-sm text-gray-500">{count}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4 sm:p-6 overflow-y-auto">
          <div className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
              <h3 className="text-lg sm:text-xl font-semibold">
                {selectedCategory === 'all' ? 'All Rules' :
                 ruleCategories.find(c => c.id === selectedCategory)?.name}
              </h3>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>{filteredRules.filter(r => r.enabled).length} enabled</span>
                <span>•</span>
                <span>{filteredRules.length} total</span>
              </div>
            </div>
            <p className="text-gray-600">
              {selectedCategory === 'all' 
                ? 'Manage all your enhancement rules in one place'
                : ruleCategories.find(c => c.id === selectedCategory)?.description}
            </p>
          </div>

          {/* Rules List */}
          <div className="space-y-4">
            {filteredRules.map((rule) => (
              <div
                key={rule.id}
                className={`border rounded-lg p-4 transition-all ${
                  rule.enabled 
                    ? 'border-gray-200 bg-white' 
                    : 'border-gray-100 bg-gray-50 opacity-75'
                }`}
              >
                <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-3 mb-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                      <h4 className="font-medium text-gray-900 truncate">{rule.name}</h4>
                      <div className="flex flex-wrap gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(rule.priority)}`}>
                          Priority {rule.priority}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(rule.metadata.estimatedImpact)}`}>
                          {rule.metadata.estimatedImpact} impact
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{rule.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>{rule.conditions.length} condition{rule.conditions.length !== 1 ? 's' : ''}</span>
                      <span>{rule.actions.length} action{rule.actions.length !== 1 ? 's' : ''}</span>
                      <span>Used {rule.usageCount} times</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 lg:flex-nowrap">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleRule(rule.id)}
                      className="flex items-center gap-1 min-h-[36px]"
                    >
                      {rule.enabled ? (
                        <>
                          <Pause className="h-4 w-4" />
                          <span className="hidden sm:inline">Disable</span>
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4" />
                          <span className="hidden sm:inline">Enable</span>
                        </>
                      )}
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onTestRule(rule)}
                      className="flex items-center gap-1 min-h-[36px]"
                    >
                      <Settings className="h-4 w-4" />
                      <span className="hidden sm:inline">Test</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditRule(rule)}
                      className="flex items-center gap-1 min-h-[36px]"
                    >
                      <Edit className="h-4 w-4" />
                      <span className="hidden sm:inline">Edit</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDuplicateRule(rule)}
                      className="flex items-center gap-1 min-h-[36px]"
                    >
                      <Copy className="h-4 w-4" />
                      <span className="hidden sm:inline">Copy</span>
                    </Button>

                    {!rule.isPublic && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteRule(rule.id)}
                        className="flex items-center gap-1 text-red-600 hover:text-red-700 min-h-[36px]"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="hidden sm:inline">Delete</span>
                      </Button>
                    )}
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  {rule.metadata.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {filteredRules.length === 0 && (
            <div className="text-center py-12">
              <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No rules found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'Try adjusting your search terms' : 'Create your first enhancement rule to get started'}
              </p>
              <Button onClick={onCreateRule}>
                <Plus className="h-4 w-4 mr-2" />
                Create New Rule
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
