import { EnhancementRule, RuleCategory } from '@/types';

export const ruleCategories: { id: RuleCategory; name: string; description: string; icon: string }[] = [
  {
    id: 'content-enhancement',
    name: 'Content Enhancement',
    description: 'Rules that improve content quality and depth',
    icon: '✨'
  },
  {
    id: 'formatting',
    name: 'Formatting',
    description: 'Rules that improve text structure and presentation',
    icon: '📝'
  },
  {
    id: 'domain-specific',
    name: 'Domain Specific',
    description: 'Rules tailored for specific domains or industries',
    icon: '🎯'
  },
  {
    id: 'length-optimization',
    name: 'Length Optimization',
    description: 'Rules that adjust prompt length for optimal results',
    icon: '📏'
  },
  {
    id: 'clarity-improvement',
    name: 'Clarity Improvement',
    description: 'Rules that make prompts clearer and more specific',
    icon: '🔍'
  },
  {
    id: 'context-addition',
    name: 'Context Addition',
    description: 'Rules that add helpful context and background',
    icon: '🧠'
  },
  {
    id: 'style-adjustment',
    name: 'Style Adjustment',
    description: 'Rules that modify tone and writing style',
    icon: '🎨'
  },
  {
    id: 'custom',
    name: 'Custom',
    description: 'User-defined custom rules',
    icon: '⚙️'
  }
];

export const defaultRules: EnhancementRule[] = [
  {
    id: 'short-prompt-elaboration',
    name: 'Short Prompt Elaboration',
    description: 'Adds context and detail to prompts shorter than 20 words',
    category: 'length-optimization',
    priority: 8,
    enabled: true,
    conditions: [
      {
        id: 'word-count-check',
        type: 'word_count',
        field: 'prompt',
        operator: 'less_than',
        value: 20,
        caseSensitive: false
      }
    ],
    actions: [
      {
        id: 'add-context-request',
        type: 'append',
        target: 'end',
        content: '\n\nPlease provide detailed, comprehensive information with specific examples and context where relevant.'
      }
    ],
    metadata: {
      tags: ['length', 'context', 'detail'],
      difficulty: 'beginner',
      estimatedImpact: 'high',
      compatibleProviders: ['openai', 'anthropic', 'google', 'cohere'],
      domain: 'general'
    },
    isPublic: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: 'code-context-enhancement',
    name: 'Code Context Enhancement',
    description: 'Adds programming context and best practices for code-related prompts',
    category: 'domain-specific',
    priority: 9,
    enabled: true,
    conditions: [
      {
        id: 'code-domain-check',
        type: 'domain',
        field: 'prompt',
        operator: 'equals',
        value: 'code',
        caseSensitive: false
      }
    ],
    actions: [
      {
        id: 'add-code-context',
        type: 'prepend',
        target: 'beginning',
        content: 'As an expert software developer, please provide code that follows best practices, includes proper error handling, and is well-documented with comments.\n\n'
      },
      {
        id: 'add-code-requirements',
        type: 'append',
        target: 'end',
        content: '\n\nPlease include:\n- Clear variable and function names\n- Proper error handling\n- Inline comments explaining complex logic\n- Consider performance and security implications'
      }
    ],
    metadata: {
      tags: ['programming', 'code', 'best-practices'],
      difficulty: 'intermediate',
      estimatedImpact: 'high',
      compatibleProviders: ['openai', 'anthropic', 'google'],
      domain: 'programming'
    },
    isPublic: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: 'business-formality-enhancement',
    name: 'Business Formality Enhancement',
    description: 'Adds professional tone and structure to business-related prompts',
    category: 'style-adjustment',
    priority: 7,
    enabled: true,
    conditions: [
      {
        id: 'business-domain-check',
        type: 'domain',
        field: 'prompt',
        operator: 'equals',
        value: 'business',
        caseSensitive: false
      }
    ],
    actions: [
      {
        id: 'add-professional-context',
        type: 'prepend',
        target: 'beginning',
        content: 'Please provide a professional, well-structured response suitable for a business environment.\n\n'
      },
      {
        id: 'add-business-requirements',
        type: 'append',
        target: 'end',
        content: '\n\nPlease ensure the response is:\n- Professional and formal in tone\n- Structured with clear sections\n- Actionable and practical\n- Supported by relevant business principles'
      }
    ],
    metadata: {
      tags: ['business', 'professional', 'formal'],
      difficulty: 'beginner',
      estimatedImpact: 'medium',
      compatibleProviders: ['openai', 'anthropic', 'google', 'cohere'],
      domain: 'business'
    },
    isPublic: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: 'creative-inspiration-boost',
    name: 'Creative Inspiration Boost',
    description: 'Enhances creative prompts with inspiration and artistic guidance',
    category: 'content-enhancement',
    priority: 6,
    enabled: true,
    conditions: [
      {
        id: 'creative-domain-check',
        type: 'domain',
        field: 'prompt',
        operator: 'equals',
        value: 'creative',
        caseSensitive: false
      }
    ],
    actions: [
      {
        id: 'add-creative-context',
        type: 'prepend',
        target: 'beginning',
        content: 'Channel your creativity and artistic vision to provide an inspiring and original response.\n\n'
      },
      {
        id: 'add-creative-elements',
        type: 'append',
        target: 'end',
        content: '\n\nPlease incorporate:\n- Vivid imagery and descriptive language\n- Original and unique perspectives\n- Emotional depth and resonance\n- Creative techniques and artistic elements'
      }
    ],
    metadata: {
      tags: ['creative', 'artistic', 'inspiration'],
      difficulty: 'intermediate',
      estimatedImpact: 'high',
      compatibleProviders: ['openai', 'anthropic', 'google'],
      domain: 'creative'
    },
    isPublic: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: 'academic-rigor-enhancement',
    name: 'Academic Rigor Enhancement',
    description: 'Adds scholarly depth and citation requirements to academic prompts',
    category: 'domain-specific',
    priority: 8,
    enabled: true,
    conditions: [
      {
        id: 'academic-domain-check',
        type: 'domain',
        field: 'prompt',
        operator: 'equals',
        value: 'academic',
        caseSensitive: false
      }
    ],
    actions: [
      {
        id: 'add-academic-context',
        type: 'prepend',
        target: 'beginning',
        content: 'Please provide a scholarly, well-researched response with academic rigor and proper methodology.\n\n'
      },
      {
        id: 'add-academic-requirements',
        type: 'append',
        target: 'end',
        content: '\n\nPlease ensure the response includes:\n- Evidence-based arguments and analysis\n- Proper academic structure and methodology\n- Critical thinking and multiple perspectives\n- Relevant citations and references where applicable'
      }
    ],
    metadata: {
      tags: ['academic', 'scholarly', 'research'],
      difficulty: 'advanced',
      estimatedImpact: 'high',
      compatibleProviders: ['openai', 'anthropic', 'google'],
      domain: 'academic'
    },
    isPublic: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  {
    id: 'clarity-improvement-vague',
    name: 'Vague Prompt Clarification',
    description: 'Adds specificity requests to vague or unclear prompts',
    category: 'clarity-improvement',
    priority: 9,
    enabled: true,
    conditions: [
      {
        id: 'vague-keywords-check',
        type: 'contains',
        field: 'prompt',
        operator: 'contains',
        value: 'help me|tell me about|explain|what is',
        caseSensitive: false
      },
      {
        id: 'short-length-check',
        type: 'word_count',
        field: 'prompt',
        operator: 'less_than',
        value: 15,
        caseSensitive: false
      }
    ],
    actions: [
      {
        id: 'add-specificity-request',
        type: 'append',
        target: 'end',
        content: '\n\nPlease be specific and detailed in your response, including:\n- Concrete examples and use cases\n- Step-by-step explanations where relevant\n- Practical applications and implications\n- Any important considerations or limitations'
      }
    ],
    metadata: {
      tags: ['clarity', 'specificity', 'detail'],
      difficulty: 'beginner',
      estimatedImpact: 'medium',
      compatibleProviders: ['openai', 'anthropic', 'google', 'cohere'],
      domain: 'general'
    },
    isPublic: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Utility functions for rules management
export function getRulesByCategory(categoryId: string): EnhancementRule[] {
  return defaultRules.filter(rule => rule.category === categoryId);
}

export function getRuleById(id: string): EnhancementRule | undefined {
  return defaultRules.find(rule => rule.id === id);
}

export function searchRules(query: string): EnhancementRule[] {
  const lowercaseQuery = query.toLowerCase();
  return defaultRules.filter(rule => 
    rule.name.toLowerCase().includes(lowercaseQuery) ||
    rule.description.toLowerCase().includes(lowercaseQuery) ||
    rule.metadata.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}

export function getEnabledRules(): EnhancementRule[] {
  return defaultRules.filter(rule => rule.enabled);
}
