import { PromptCharacteristics } from '@/types';

export class PromptAnalyzer {
  private static readonly CODE_PATTERNS = [
    /```[\s\S]*?```/g, // Code blocks
    /`[^`]+`/g, // Inline code
    /function\s+\w+\s*\(/g, // Function declarations
    /class\s+\w+/g, // Class declarations
    /import\s+.*from/g, // Import statements
    /export\s+(default\s+)?/g, // Export statements
    /<\/?[a-z][\s\S]*?>/gi, // HTML/XML tags
    /\$\{.*?\}/g, // Template literals
    /\/\*[\s\S]*?\*\//g, // Block comments
    /\/\/.*$/gm, // Line comments
  ];

  private static readonly MATH_PATTERNS = [
    /\$\$[\s\S]*?\$\$/g, // LaTeX display math
    /\$[^$\n]+\$/g, // LaTeX inline math
    /\\[a-zA-Z]+\{[^}]*\}/g, // LaTeX commands
    /\b\d+\s*[+\-*/=]\s*\d+/g, // Basic math expressions
    /\b(sin|cos|tan|log|ln|sqrt|integral|derivative|matrix|vector)\b/gi, // Math functions
    /[∑∏∫∂∇αβγδεζηθικλμνξοπρστυφχψω]/g, // Math symbols
  ];

  private static readonly URL_PATTERNS = [
    /https?:\/\/[^\s]+/g,
    /www\.[^\s]+/g,
    /[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^\s]*/g,
  ];

  private static readonly SENSITIVE_PATTERNS = [
    /\b(password|secret|token|key|credential)\b/gi,
    /\b(violence|weapon|drug|illegal|hack|exploit)\b/gi,
    /\b(personal|private|confidential|classified)\b/gi,
  ];

  private static readonly DOMAIN_KEYWORDS = {
    code: [
      'function', 'class', 'variable', 'algorithm', 'debug', 'compile', 'syntax',
      'programming', 'software', 'development', 'api', 'database', 'framework',
      'library', 'repository', 'git', 'deployment', 'testing', 'refactor'
    ],
    creative: [
      'story', 'poem', 'creative', 'imagination', 'character', 'plot', 'narrative',
      'fiction', 'novel', 'screenplay', 'dialogue', 'metaphor', 'imagery',
      'artistic', 'design', 'inspiration', 'brainstorm', 'innovative'
    ],
    business: [
      'strategy', 'market', 'revenue', 'profit', 'customer', 'client', 'sales',
      'marketing', 'business', 'company', 'organization', 'management', 'leadership',
      'finance', 'budget', 'investment', 'roi', 'kpi', 'metrics', 'analysis'
    ],
    academic: [
      'research', 'study', 'analysis', 'theory', 'hypothesis', 'methodology',
      'literature', 'citation', 'peer-review', 'academic', 'scholarly', 'thesis',
      'dissertation', 'journal', 'publication', 'experiment', 'data', 'statistics'
    ],
    technical: [
      'technical', 'specification', 'documentation', 'manual', 'guide', 'tutorial',
      'implementation', 'architecture', 'system', 'infrastructure', 'protocol',
      'standard', 'compliance', 'security', 'performance', 'optimization'
    ],
    conversational: [
      'chat', 'conversation', 'discuss', 'talk', 'dialogue', 'question', 'answer',
      'help', 'explain', 'clarify', 'understand', 'opinion', 'advice', 'suggestion'
    ]
  };

  private static readonly COMPLEXITY_INDICATORS = {
    simple: [
      'what', 'how', 'when', 'where', 'who', 'simple', 'basic', 'easy', 'quick'
    ],
    moderate: [
      'explain', 'describe', 'compare', 'analyze', 'evaluate', 'discuss', 'outline'
    ],
    complex: [
      'synthesize', 'integrate', 'comprehensive', 'detailed', 'thorough', 'complex',
      'sophisticated', 'advanced', 'multi-step', 'interdisciplinary'
    ],
    very_complex: [
      'systematic', 'holistic', 'paradigm', 'framework', 'methodology', 'theoretical',
      'philosophical', 'meta-analysis', 'cross-disciplinary', 'revolutionary'
    ]
  };

  public static analyze(prompt: string): PromptCharacteristics {
    const cleanPrompt = prompt.trim().toLowerCase();
    const words = cleanPrompt.split(/\s+/).filter(word => word.length > 0);
    const sentences = prompt.split(/[.!?]+/).filter(s => s.trim().length > 0);

    return {
      length: this.analyzeLength(words.length),
      complexity: this.analyzeComplexity(cleanPrompt, words),
      domain: this.analyzeDomain(cleanPrompt),
      intent: this.analyzeIntent(cleanPrompt),
      language_style: this.analyzeLanguageStyle(cleanPrompt),
      safety_level: this.analyzeSafetyLevel(prompt),
      performance_requirement: this.analyzePerformanceRequirement(cleanPrompt),
      word_count: words.length,
      sentence_count: sentences.length,
      has_code: this.hasCode(prompt),
      has_math: this.hasMath(prompt),
      has_urls: this.hasUrls(prompt),
      has_sensitive_content: this.hasSensitiveContent(prompt)
    };
  }

  private static analyzeLength(wordCount: number): PromptCharacteristics['length'] {
    if (wordCount <= 20) return 'short';
    if (wordCount <= 100) return 'medium';
    if (wordCount <= 500) return 'long';
    return 'very_long';
  }

  private static analyzeComplexity(prompt: string, words: string[]): PromptCharacteristics['complexity'] {
    let complexityScore = 0;

    // Check for complexity indicators
    for (const [level, indicators] of Object.entries(this.COMPLEXITY_INDICATORS)) {
      const matches = indicators.filter(indicator => prompt.includes(indicator)).length;
      switch (level) {
        case 'simple':
          complexityScore += matches * 1;
          break;
        case 'moderate':
          complexityScore += matches * 2;
          break;
        case 'complex':
          complexityScore += matches * 3;
          break;
        case 'very_complex':
          complexityScore += matches * 4;
          break;
      }
    }

    // Additional complexity factors
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    if (avgWordLength > 6) complexityScore += 2;

    const sentenceComplexity = prompt.split(/[.!?]+/).reduce((sum, sentence) => {
      const clauseCount = (sentence.match(/[,;:]/g) || []).length + 1;
      return sum + clauseCount;
    }, 0) / prompt.split(/[.!?]+/).length;

    if (sentenceComplexity > 2) complexityScore += 2;

    // Normalize score
    if (complexityScore <= 3) return 'simple';
    if (complexityScore <= 8) return 'moderate';
    if (complexityScore <= 15) return 'complex';
    return 'very_complex';
  }

  private static analyzeDomain(prompt: string): PromptCharacteristics['domain'] {
    const domainScores: Record<string, number> = {};

    for (const [domain, keywords] of Object.entries(this.DOMAIN_KEYWORDS)) {
      domainScores[domain] = keywords.filter(keyword => prompt.includes(keyword)).length;
    }

    const maxScore = Math.max(...Object.values(domainScores));
    if (maxScore === 0) return 'general';

    const topDomain = Object.entries(domainScores).find(([_, score]) => score === maxScore)?.[0];
    return (topDomain as PromptCharacteristics['domain']) || 'general';
  }

  private static analyzeIntent(prompt: string): PromptCharacteristics['intent'] {
    const intentPatterns = {
      generation: /\b(create|generate|write|produce|make|build|compose|draft)\b/gi,
      analysis: /\b(analyze|examine|evaluate|assess|review|study|investigate)\b/gi,
      conversation: /\b(chat|discuss|talk|conversation|dialogue|ask|tell)\b/gi,
      instruction: /\b(how to|step by step|guide|tutorial|instructions|explain how)\b/gi,
      creative: /\b(creative|imaginative|artistic|original|innovative|brainstorm)\b/gi,
      problem_solving: /\b(solve|fix|debug|troubleshoot|resolve|problem|issue|challenge)\b/gi
    };

    for (const [intent, pattern] of Object.entries(intentPatterns)) {
      if (pattern.test(prompt)) {
        return intent as PromptCharacteristics['intent'];
      }
    }

    return 'generation'; // Default intent
  }

  private static analyzeLanguageStyle(prompt: string): PromptCharacteristics['language_style'] {
    const formalIndicators = ['please', 'kindly', 'would you', 'could you', 'furthermore', 'therefore', 'consequently'];
    const casualIndicators = ['hey', 'hi', 'yeah', 'ok', 'cool', 'awesome', 'gonna', 'wanna'];
    const technicalIndicators = ['implement', 'configure', 'optimize', 'algorithm', 'protocol', 'specification'];
    const academicIndicators = ['research', 'hypothesis', 'methodology', 'literature', 'peer-reviewed', 'empirical'];
    const creativeIndicators = ['imagine', 'envision', 'creative', 'artistic', 'expressive', 'poetic'];

    const formalScore = formalIndicators.filter(indicator => prompt.toLowerCase().includes(indicator)).length;
    const casualScore = casualIndicators.filter(indicator => prompt.toLowerCase().includes(indicator)).length;
    const technicalScore = technicalIndicators.filter(indicator => prompt.toLowerCase().includes(indicator)).length;
    const academicScore = academicIndicators.filter(indicator => prompt.toLowerCase().includes(indicator)).length;
    const creativeScore = creativeIndicators.filter(indicator => prompt.toLowerCase().includes(indicator)).length;

    const scores = { formal: formalScore, casual: casualScore, technical: technicalScore, academic: academicScore, creative: creativeScore };
    const maxScore = Math.max(...Object.values(scores));

    if (maxScore === 0) return 'formal'; // Default to formal

    const topStyle = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0];
    return (topStyle as PromptCharacteristics['language_style']) || 'formal';
  }

  private static analyzeSafetyLevel(prompt: string): PromptCharacteristics['safety_level'] {
    const sensitiveMatches = this.SENSITIVE_PATTERNS.reduce((count, pattern) => {
      return count + (prompt.match(pattern) || []).length;
    }, 0);

    if (sensitiveMatches >= 3) return 'critical';
    if (sensitiveMatches >= 2) return 'high';
    if (sensitiveMatches >= 1) return 'medium';
    return 'low';
  }

  private static analyzePerformanceRequirement(prompt: string): PromptCharacteristics['performance_requirement'] {
    const speedIndicators = ['quick', 'fast', 'rapid', 'immediate', 'urgent', 'asap'];
    const qualityIndicators = ['detailed', 'thorough', 'comprehensive', 'high-quality', 'excellent', 'perfect'];

    const speedScore = speedIndicators.filter(indicator => prompt.includes(indicator)).length;
    const qualityScore = qualityIndicators.filter(indicator => prompt.includes(indicator)).length;

    if (speedScore > qualityScore) return 'speed';
    if (qualityScore > speedScore) return 'quality';
    return 'balanced';
  }

  private static hasCode(prompt: string): boolean {
    return this.CODE_PATTERNS.some(pattern => pattern.test(prompt));
  }

  private static hasMath(prompt: string): boolean {
    return this.MATH_PATTERNS.some(pattern => pattern.test(prompt));
  }

  private static hasUrls(prompt: string): boolean {
    return this.URL_PATTERNS.some(pattern => pattern.test(prompt));
  }

  private static hasSensitiveContent(prompt: string): boolean {
    return this.SENSITIVE_PATTERNS.some(pattern => pattern.test(prompt));
  }
}
