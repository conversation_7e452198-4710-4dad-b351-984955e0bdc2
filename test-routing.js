// Simple test to verify the routing system works
const { PromptAnalyzer } = require('./src/lib/prompt-analyzer.ts');
const { providerRouter } = require('./src/lib/provider-router.ts');

async function testRouting() {
  console.log('Testing QueryBoost Intelligent Routing System...\n');

  const testPrompts = [
    {
      name: 'Code Generation',
      prompt: 'Write a Python function to calculate fibonacci numbers with memoization'
    },
    {
      name: 'Creative Writing',
      prompt: 'Write a short story about a time traveler who gets stuck in the Renaissance period'
    },
    {
      name: 'Complex Analysis',
      prompt: 'Analyze the economic implications of artificial intelligence on global labor markets, considering both short-term disruptions and long-term adaptations across different sectors'
    },
    {
      name: 'Simple Question',
      prompt: 'What is the capital of France?'
    },
    {
      name: 'Math Problem',
      prompt: 'Solve this differential equation: dy/dx = 2x + 3y, with initial condition y(0) = 1'
    }
  ];

  for (const test of testPrompts) {
    console.log(`\n=== ${test.name} ===`);
    console.log(`Prompt: "${test.prompt}"`);
    
    try {
      // Analyze prompt characteristics
      const characteristics = PromptAnalyzer.analyze(test.prompt);
      console.log(`\nCharacteristics:`);
      console.log(`- Length: ${characteristics.length}`);
      console.log(`- Complexity: ${characteristics.complexity}`);
      console.log(`- Domain: ${characteristics.domain}`);
      console.log(`- Intent: ${characteristics.intent}`);
      console.log(`- Has Code: ${characteristics.has_code}`);
      console.log(`- Has Math: ${characteristics.has_math}`);
      
      // Get routing decision
      const decision = await providerRouter.routeProvider(test.prompt);
      console.log(`\nRouting Decision:`);
      console.log(`- Selected Provider: ${decision.selected_provider.toUpperCase()}`);
      console.log(`- Score: ${decision.score}/10`);
      console.log(`- Confidence: ${(decision.confidence * 100).toFixed(1)}%`);
      console.log(`- Analysis Time: ${decision.analysis_time_ms}ms`);
      console.log(`- Strategy: ${decision.routing_strategy.name}`);
      
      console.log(`\nReasoning:`);
      decision.reasoning.forEach((reason, index) => {
        console.log(`  ${index + 1}. ${reason}`);
      });
      
      if (decision.alternatives.length > 0) {
        console.log(`\nAlternatives:`);
        decision.alternatives.slice(0, 3).forEach((alt, index) => {
          console.log(`  ${index + 1}. ${alt.provider.toUpperCase()} (${alt.score.toFixed(1)})`);
        });
      }
      
    } catch (error) {
      console.error(`Error testing "${test.name}":`, error.message);
    }
    
    console.log('\n' + '='.repeat(60));
  }
  
  console.log('\n✅ Routing system test completed!');
}

// Run the test
testRouting().catch(console.error);
