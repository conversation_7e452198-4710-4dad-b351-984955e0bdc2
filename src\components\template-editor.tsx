'use client';

import { useState, useEffect } from 'react';
import { Template } from '@/types';
import { processTemplateVariables } from '@/lib/templates';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Eye, EyeOff, Co<PERSON>, Check } from 'lucide-react';

interface TemplateEditorProps {
  template: Template;
  onComplete: (processedPrompt: string) => void;
  onBack: () => void;
}

export function TemplateEditor({ template, onComplete, onBack }: TemplateEditorProps) {
  const [variables, setVariables] = useState<Record<string, string>>({});
  const [showPreview, setShowPreview] = useState(false);
  const [processedPrompt, setProcessedPrompt] = useState('');
  const [copied, setCopied] = useState(false);

  // Initialize variables with empty strings
  useEffect(() => {
    const initialVariables: Record<string, string> = {};
    template.variables.forEach(variable => {
      initialVariables[variable] = '';
    });
    setVariables(initialVariables);
  }, [template]);

  // Update processed prompt when variables change
  useEffect(() => {
    const processed = processTemplateVariables(template.prompt, variables);
    setProcessedPrompt(processed);
  }, [template.prompt, variables]);

  const handleVariableChange = (variable: string, value: string) => {
    setVariables(prev => ({
      ...prev,
      [variable]: value
    }));
  };

  const handleComplete = () => {
    onComplete(processedPrompt);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(processedPrompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const isComplete = template.variables.every(variable => variables[variable]?.trim());

  const getVariableDescription = (variable: string): string => {
    // Provide helpful descriptions for common variable patterns
    const descriptions: Record<string, string> = {
      'TOPIC': 'The main subject or theme you want to focus on',
      'AUDIENCE': 'Your target audience (e.g., beginners, professionals, students)',
      'TONE': 'The writing style (e.g., formal, casual, friendly, professional)',
      'WORD_COUNT': 'Desired length (e.g., 500 words, 1000 words)',
      'BRAND_VOICE': 'Your brand personality (e.g., authoritative, playful, trustworthy)',
      'TARGET_AUDIENCE': 'Specific demographic or user group',
      'GOAL': 'What you want to achieve (e.g., increase awareness, drive sales)',
      'PROGRAMMING_LANGUAGE': 'The coding language (e.g., JavaScript, Python, Java)',
      'FRAMEWORK': 'Technology framework (e.g., React, Django, Spring)',
      'PROJECT_TYPE': 'Type of project (e.g., web app, mobile app, API)',
      'COMPANY/PRODUCT': 'Your company or product name',
      'INDUSTRY': 'Business sector (e.g., technology, healthcare, finance)',
      'CLIENT_NAME': 'Name of the client or organization',
      'BUDGET_RANGE': 'Estimated budget (e.g., $10k-50k, $100k+)',
      'TIMELINE': 'Project duration (e.g., 3 months, 6 weeks)',
      'GRADE_LEVEL': 'Educational level (e.g., elementary, high school, college)',
      'DURATION': 'Time length (e.g., 45 minutes, 2 hours)',
      'SUBJECT/TOPIC': 'Academic subject or specific topic to teach'
    };

    return descriptions[variable] || `Enter value for ${variable.toLowerCase().replace(/_/g, ' ')}`;
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={onBack} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h2 className="text-2xl font-bold">{template.title}</h2>
          <p className="text-gray-600">{template.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Variable Input Form */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold mb-4">Fill in the Variables</h3>
          
          {template.variables.map((variable, index) => (
            <div key={variable} className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                {variable.replace(/_/g, ' ').replace(/\//g, ' / ')}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <textarea
                value={variables[variable] || ''}
                onChange={(e) => handleVariableChange(variable, e.target.value)}
                placeholder={getVariableDescription(variable)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={2}
              />
              <p className="text-xs text-gray-500">
                {getVariableDescription(variable)}
              </p>
            </div>
          ))}

          <div className="pt-4">
            <Button
              onClick={handleComplete}
              disabled={!isComplete}
              className="w-full"
            >
              Use This Prompt
            </Button>
          </div>
        </div>

        {/* Preview Panel */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Preview</h3>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
                className="flex items-center gap-2"
              >
                {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showPreview ? 'Hide' : 'Show'} Preview
              </Button>
              {showPreview && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyToClipboard}
                  className="flex items-center gap-2"
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  {copied ? 'Copied!' : 'Copy'}
                </Button>
              )}
            </div>
          </div>

          {showPreview ? (
            <div className="bg-gray-50 border rounded-lg p-4 max-h-96 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono">
                {processedPrompt}
              </pre>
            </div>
          ) : (
            <div className="bg-gray-50 border rounded-lg p-4 h-32 flex items-center justify-center">
              <p className="text-gray-500 text-center">
                Fill in the variables above to see the preview
              </p>
            </div>
          )}

          {/* Template Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Template Information</h4>
            <div className="space-y-1 text-sm text-blue-800">
              <p><span className="font-medium">Category:</span> {template.category.replace('-', ' ')}</p>
              <p><span className="font-medium">Difficulty:</span> {template.difficulty}</p>
              <p><span className="font-medium">Estimated Tokens:</span> {template.estimatedTokens}</p>
              <p><span className="font-medium">Variables:</span> {template.variables.length}</p>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="bg-white border rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Completion Progress</span>
              <span className="text-sm text-gray-500">
                {Object.values(variables).filter(v => v.trim()).length} / {template.variables.length}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${(Object.values(variables).filter(v => v.trim()).length / template.variables.length) * 100}%`
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
