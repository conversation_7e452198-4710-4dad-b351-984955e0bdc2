import { EnhancementRule, RuleCondition, RuleAction, RuleEvaluationResult } from '@/types';

export class RulesEngine {
  private rules: EnhancementRule[] = [];

  constructor(rules: EnhancementRule[] = []) {
    this.rules = rules.filter(rule => rule.enabled).sort((a, b) => b.priority - a.priority);
  }

  /**
   * Apply all applicable rules to a prompt
   */
  async applyRules(prompt: string, metadata?: Record<string, any>): Promise<{
    enhancedPrompt: string;
    appliedRules: RuleEvaluationResult[];
  }> {
    let currentPrompt = prompt;
    const appliedRules: RuleEvaluationResult[] = [];

    for (const rule of this.rules) {
      const startTime = Date.now();
      
      try {
        const conditionsMet = await this.evaluateConditions(rule.conditions, currentPrompt, metadata);
        
        if (conditionsMet) {
          const beforeText = currentPrompt;
          currentPrompt = await this.applyActions(rule.actions, currentPrompt);
          const afterText = currentPrompt;
          
          appliedRules.push({
            ruleId: rule.id,
            ruleName: rule.name,
            applied: true,
            beforeText,
            afterText,
            executionTime: Date.now() - startTime
          });
        } else {
          appliedRules.push({
            ruleId: rule.id,
            ruleName: rule.name,
            applied: false,
            reason: 'Conditions not met',
            executionTime: Date.now() - startTime
          });
        }
      } catch (error) {
        appliedRules.push({
          ruleId: rule.id,
          ruleName: rule.name,
          applied: false,
          reason: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          executionTime: Date.now() - startTime
        });
      }
    }

    return {
      enhancedPrompt: currentPrompt,
      appliedRules
    };
  }

  /**
   * Evaluate all conditions for a rule
   */
  private async evaluateConditions(
    conditions: RuleCondition[], 
    prompt: string, 
    metadata?: Record<string, any>
  ): Promise<boolean> {
    if (conditions.length === 0) return true;

    // All conditions must be true (AND logic)
    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, prompt, metadata);
      if (!result) return false;
    }

    return true;
  }

  /**
   * Evaluate a single condition
   */
  private async evaluateCondition(
    condition: RuleCondition, 
    prompt: string, 
    metadata?: Record<string, any>
  ): Promise<boolean> {
    let targetValue: string | number;
    
    switch (condition.field) {
      case 'prompt':
        targetValue = prompt;
        break;
      case 'metadata':
        targetValue = metadata?.[condition.value as string] || '';
        break;
      default:
        targetValue = prompt;
    }

    let result = false;

    switch (condition.type) {
      case 'contains':
        result = this.evaluateContains(targetValue as string, condition);
        break;
      case 'length':
        result = this.evaluateLength(targetValue as string, condition);
        break;
      case 'word_count':
        result = this.evaluateWordCount(targetValue as string, condition);
        break;
      case 'starts_with':
        result = this.evaluateStartsWith(targetValue as string, condition);
        break;
      case 'ends_with':
        result = this.evaluateEndsWith(targetValue as string, condition);
        break;
      case 'regex':
        result = this.evaluateRegex(targetValue as string, condition);
        break;
      case 'domain':
        result = this.evaluateDomain(targetValue as string, condition);
        break;
      case 'complexity':
        result = this.evaluateComplexity(targetValue as string, condition);
        break;
      default:
        result = false;
    }

    return condition.negate ? !result : result;
  }

  private evaluateContains(text: string, condition: RuleCondition): boolean {
    const searchText = condition.caseSensitive ? text : text.toLowerCase();
    const searchValue = condition.caseSensitive ? 
      condition.value as string : 
      (condition.value as string).toLowerCase();

    switch (condition.operator) {
      case 'contains':
        return searchText.includes(searchValue);
      case 'not_contains':
        return !searchText.includes(searchValue);
      case 'equals':
        return searchText === searchValue;
      default:
        return false;
    }
  }

  private evaluateLength(text: string, condition: RuleCondition): boolean {
    const length = text.length;
    const value = condition.value as number;

    switch (condition.operator) {
      case 'greater_than':
        return length > value;
      case 'less_than':
        return length < value;
      case 'equals':
        return length === value;
      case 'between':
        const secondValue = condition.secondaryValue as number;
        return length >= value && length <= secondValue;
      default:
        return false;
    }
  }

  private evaluateWordCount(text: string, condition: RuleCondition): boolean {
    const wordCount = text.trim().split(/\s+/).length;
    const value = condition.value as number;

    switch (condition.operator) {
      case 'greater_than':
        return wordCount > value;
      case 'less_than':
        return wordCount < value;
      case 'equals':
        return wordCount === value;
      case 'between':
        const secondValue = condition.secondaryValue as number;
        return wordCount >= value && wordCount <= secondValue;
      default:
        return false;
    }
  }

  private evaluateStartsWith(text: string, condition: RuleCondition): boolean {
    const searchText = condition.caseSensitive ? text : text.toLowerCase();
    const searchValue = condition.caseSensitive ? 
      condition.value as string : 
      (condition.value as string).toLowerCase();

    return searchText.startsWith(searchValue);
  }

  private evaluateEndsWith(text: string, condition: RuleCondition): boolean {
    const searchText = condition.caseSensitive ? text : text.toLowerCase();
    const searchValue = condition.caseSensitive ? 
      condition.value as string : 
      (condition.value as string).toLowerCase();

    return searchText.endsWith(searchValue);
  }

  private evaluateRegex(text: string, condition: RuleCondition): boolean {
    try {
      const flags = condition.caseSensitive ? 'g' : 'gi';
      const regex = new RegExp(condition.value as string, flags);
      return regex.test(text);
    } catch (error) {
      console.error('Invalid regex pattern:', condition.value, error);
      return false;
    }
  }

  private evaluateDomain(text: string, condition: RuleCondition): boolean {
    const domains = {
      'code': ['code', 'programming', 'function', 'class', 'variable', 'algorithm', 'debug'],
      'business': ['business', 'strategy', 'market', 'revenue', 'profit', 'customer'],
      'creative': ['story', 'creative', 'narrative', 'character', 'plot', 'artistic'],
      'academic': ['research', 'study', 'analysis', 'theory', 'academic', 'scholarly'],
      'technical': ['technical', 'system', 'architecture', 'infrastructure', 'engineering']
    };

    const domainKeywords = domains[condition.value as keyof typeof domains] || [];
    const lowerText = text.toLowerCase();
    
    return domainKeywords.some(keyword => lowerText.includes(keyword));
  }

  private evaluateComplexity(text: string, condition: RuleCondition): boolean {
    // Simple complexity score based on sentence length, vocabulary, and structure
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.trim().split(/\s+/);
    const avgSentenceLength = words.length / sentences.length;
    const uniqueWords = new Set(words.map(w => w.toLowerCase())).size;
    const vocabularyRatio = uniqueWords / words.length;
    
    // Complexity score from 1-10
    let complexityScore = 1;
    if (avgSentenceLength > 15) complexityScore += 2;
    if (avgSentenceLength > 25) complexityScore += 2;
    if (vocabularyRatio > 0.7) complexityScore += 2;
    if (vocabularyRatio > 0.8) complexityScore += 2;
    if (text.includes(',') || text.includes(';')) complexityScore += 1;

    const value = condition.value as number;

    switch (condition.operator) {
      case 'greater_than':
        return complexityScore > value;
      case 'less_than':
        return complexityScore < value;
      case 'equals':
        return complexityScore === value;
      default:
        return false;
    }
  }

  /**
   * Apply all actions for a rule
   */
  private async applyActions(actions: RuleAction[], prompt: string): Promise<string> {
    let result = prompt;

    for (const action of actions) {
      result = await this.applyAction(action, result);
    }

    return result;
  }

  /**
   * Apply a single action
   */
  private async applyAction(action: RuleAction, prompt: string): Promise<string> {
    switch (action.type) {
      case 'prepend':
        return action.content + '\n\n' + prompt;
      
      case 'append':
        return prompt + '\n\n' + action.content;
      
      case 'wrap':
        return action.content.replace('{content}', prompt);
      
      case 'replace':
        // Simple replace - in a real implementation, this could be more sophisticated
        return prompt.replace(new RegExp(action.target, 'gi'), action.content);
      
      case 'format':
        return this.applyFormatting(prompt, action);
      
      case 'insert_at':
        if (action.position !== undefined) {
          const pos = Math.min(action.position, prompt.length);
          return prompt.slice(0, pos) + action.content + prompt.slice(pos);
        }
        return prompt;
      
      default:
        return prompt;
    }
  }

  private applyFormatting(text: string, action: RuleAction): string {
    if (!action.formatting) return text;

    switch (action.formatting.style) {
      case 'bold':
        return `**${text}**`;
      case 'italic':
        return `*${text}*`;
      case 'code':
        return `\`${text}\``;
      case 'quote':
        return `> ${text}`;
      case 'list':
        return text.split('\n').map(line => `- ${line}`).join('\n');
      case 'heading':
        const level = action.formatting.level || 1;
        const hashes = '#'.repeat(level);
        return `${hashes} ${text}`;
      default:
        return text;
    }
  }

  /**
   * Test a rule against a sample prompt
   */
  async testRule(rule: EnhancementRule, prompt: string): Promise<RuleEvaluationResult> {
    const engine = new RulesEngine([rule]);
    const result = await engine.applyRules(prompt);
    return result.appliedRules[0];
  }
}
