'use client';

import { useState, useEffect } from 'react';
import { EnhancementRule, RuleCondition, RuleAction, RuleCategory } from '@/types';
import { ruleCategories } from '@/lib/default-rules';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Plus, Trash2, Play } from 'lucide-react';

interface RuleEditorProps {
  rule?: EnhancementRule;
  onSave: (rule: EnhancementRule) => void;
  onBack: () => void;
  onTest: (rule: EnhancementRule) => void;
}

export function RuleEditor({ rule, onSave, onBack, onTest }: RuleEditorProps) {
  const [formData, setFormData] = useState<Partial<EnhancementRule>>({
    name: '',
    description: '',
    category: 'custom',
    priority: 5,
    enabled: true,
    conditions: [],
    actions: [],
    metadata: {
      tags: [],
      difficulty: 'beginner',
      estimatedImpact: 'medium',
      compatibleProviders: ['openai', 'anthropic', 'google', 'cohere'],
      domain: 'general'
    },
    isPublic: false,
    usageCount: 0
  });

  useEffect(() => {
    if (rule) {
      setFormData(rule);
    }
  }, [rule]);

  const handleSave = () => {
    const now = new Date().toISOString();
    const ruleToSave: EnhancementRule = {
      id: rule?.id || `rule-${Date.now()}`,
      name: formData.name || '',
      description: formData.description || '',
      category: formData.category || 'custom',
      priority: formData.priority || 5,
      enabled: formData.enabled ?? true,
      conditions: formData.conditions || [],
      actions: formData.actions || [],
      metadata: formData.metadata || {
        tags: [],
        difficulty: 'beginner',
        estimatedImpact: 'medium',
        compatibleProviders: ['openai', 'anthropic', 'google', 'cohere'],
        domain: 'general'
      },
      isPublic: formData.isPublic || false,
      usageCount: formData.usageCount || 0,
      createdAt: rule?.createdAt || now,
      updatedAt: now
    };

    onSave(ruleToSave);
  };

  const handleTest = () => {
    const testRule: EnhancementRule = {
      id: 'test-rule',
      name: formData.name || 'Test Rule',
      description: formData.description || '',
      category: formData.category || 'custom',
      priority: formData.priority || 5,
      enabled: true,
      conditions: formData.conditions || [],
      actions: formData.actions || [],
      metadata: formData.metadata || {
        tags: [],
        difficulty: 'beginner',
        estimatedImpact: 'medium',
        compatibleProviders: ['openai', 'anthropic', 'google', 'cohere'],
        domain: 'general'
      },
      isPublic: false,
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    onTest(testRule);
  };

  const addCondition = () => {
    const newCondition: RuleCondition = {
      id: `condition-${Date.now()}`,
      type: 'contains',
      field: 'prompt',
      operator: 'contains',
      value: '',
      caseSensitive: false
    };

    setFormData(prev => ({
      ...prev,
      conditions: [...(prev.conditions || []), newCondition]
    }));
  };

  const updateCondition = (index: number, updates: Partial<RuleCondition>) => {
    setFormData(prev => ({
      ...prev,
      conditions: prev.conditions?.map((condition, i) => 
        i === index ? { ...condition, ...updates } : condition
      ) || []
    }));
  };

  const removeCondition = (index: number) => {
    setFormData(prev => ({
      ...prev,
      conditions: prev.conditions?.filter((_, i) => i !== index) || []
    }));
  };

  const addAction = () => {
    const newAction: RuleAction = {
      id: `action-${Date.now()}`,
      type: 'append',
      target: 'end',
      content: ''
    };

    setFormData(prev => ({
      ...prev,
      actions: [...(prev.actions || []), newAction]
    }));
  };

  const updateAction = (index: number, updates: Partial<RuleAction>) => {
    setFormData(prev => ({
      ...prev,
      actions: prev.actions?.map((action, i) => 
        i === index ? { ...action, ...updates } : action
      ) || []
    }));
  };

  const removeAction = (index: number) => {
    setFormData(prev => ({
      ...prev,
      actions: prev.actions?.filter((_, i) => i !== index) || []
    }));
  };

  const isValid = formData.name && formData.description && (formData.conditions?.length || 0) > 0 && (formData.actions?.length || 0) > 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
          <Button variant="ghost" onClick={onBack} className="self-start p-2 min-h-[44px] min-w-[44px]">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1 min-w-0">
            <h2 className="text-xl sm:text-2xl font-bold">
              {rule ? 'Edit Rule' : 'Create New Rule'}
            </h2>
            <p className="text-sm sm:text-base text-gray-600">
              Define conditions and actions for automatic prompt enhancement
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button variant="outline" onClick={handleTest} disabled={!isValid} className="min-h-[44px]">
              <Play className="h-4 w-4 mr-2" />
              Test Rule
            </Button>
            <Button onClick={handleSave} disabled={!isValid} className="min-h-[44px]">
              Save Rule
            </Button>
          </div>
        </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white border rounded-lg p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-4">Basic Information</h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rule Name *
              </label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter a descriptive name for your rule"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={formData.category || 'custom'}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as RuleCategory }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {ruleCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority (1-10)
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.priority || 5}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">Higher numbers have higher priority</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <div className="flex flex-col sm:flex-row gap-4">
                <label className="flex items-center min-h-[44px]">
                  <input
                    type="checkbox"
                    checked={formData.enabled ?? true}
                    onChange={(e) => setFormData(prev => ({ ...prev, enabled: e.target.checked }))}
                    className="mr-2 min-h-[20px] min-w-[20px]"
                  />
                  Enabled
                </label>
                <label className="flex items-center min-h-[44px]">
                  <input
                    type="checkbox"
                    checked={formData.isPublic || false}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                    className="mr-2 min-h-[20px] min-w-[20px]"
                  />
                  Public
                </label>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this rule does and when it should be applied"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Conditions */}
        <div className="bg-white border rounded-lg p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
            <h3 className="text-lg font-semibold">Conditions</h3>
            <Button onClick={addCondition} size="sm" className="min-h-[40px] w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Condition
            </Button>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">
            Define when this rule should be triggered. All conditions must be met.
          </p>

          <div className="space-y-4">
            {formData.conditions?.map((condition, index) => (
              <div key={condition.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium">Condition {index + 1}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCondition(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Type</label>
                    <select
                      value={condition.type}
                      onChange={(e) => updateCondition(index, { type: e.target.value as any })}
                      className="w-full px-2 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 min-h-[40px]"
                    >
                      <option value="contains">Contains</option>
                      <option value="length">Length</option>
                      <option value="word_count">Word Count</option>
                      <option value="starts_with">Starts With</option>
                      <option value="ends_with">Ends With</option>
                      <option value="regex">Regex</option>
                      <option value="domain">Domain</option>
                      <option value="complexity">Complexity</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Operator</label>
                    <select
                      value={condition.operator}
                      onChange={(e) => updateCondition(index, { operator: e.target.value as any })}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="contains">Contains</option>
                      <option value="equals">Equals</option>
                      <option value="greater_than">Greater Than</option>
                      <option value="less_than">Less Than</option>
                      <option value="between">Between</option>
                      <option value="matches">Matches</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Value</label>
                    <input
                      type={['length', 'word_count', 'complexity'].includes(condition.type) ? 'number' : 'text'}
                      value={condition.value}
                      onChange={(e) => updateCondition(index, { 
                        value: ['length', 'word_count', 'complexity'].includes(condition.type) 
                          ? parseInt(e.target.value) || 0 
                          : e.target.value 
                      })}
                      placeholder="Enter value"
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Options</label>
                    <div className="flex items-center gap-2">
                      <label className="flex items-center text-xs">
                        <input
                          type="checkbox"
                          checked={condition.caseSensitive || false}
                          onChange={(e) => updateCondition(index, { caseSensitive: e.target.checked })}
                          className="mr-1"
                        />
                        Case Sensitive
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {(formData.conditions?.length || 0) === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>No conditions defined. Click "Add Condition" to get started.</p>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white border rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Actions</h3>
            <Button onClick={addAction} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Action
            </Button>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">
            Define what changes to make when conditions are met.
          </p>

          <div className="space-y-4">
            {formData.actions?.map((action, index) => (
              <div key={action.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium">Action {index + 1}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAction(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Type</label>
                    <select
                      value={action.type}
                      onChange={(e) => updateAction(index, { type: e.target.value as any })}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="prepend">Prepend</option>
                      <option value="append">Append</option>
                      <option value="replace">Replace</option>
                      <option value="wrap">Wrap</option>
                      <option value="insert_at">Insert At Position</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Target</label>
                    <select
                      value={action.target}
                      onChange={(e) => updateAction(index, { target: e.target.value as any })}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="beginning">Beginning</option>
                      <option value="end">End</option>
                      <option value="entire">Entire Text</option>
                      <option value="match">Match</option>
                      <option value="position">Position</option>
                    </select>
                  </div>

                  {action.target === 'position' && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Position</label>
                      <input
                        type="number"
                        value={action.position || 0}
                        onChange={(e) => updateAction(index, { position: parseInt(e.target.value) || 0 })}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Content</label>
                  <textarea
                    value={action.content}
                    onChange={(e) => updateAction(index, { content: e.target.value })}
                    placeholder="Enter the text to add or replacement content"
                    rows={3}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                  />
                </div>
              </div>
            ))}

            {(formData.actions?.length || 0) === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>No actions defined. Click "Add Action" to get started.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
