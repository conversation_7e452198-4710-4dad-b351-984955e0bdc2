import { 
  AIProvider, 
  PromptCharacteristics, 
  ProviderScore, 
  RoutingDecision, 
  RoutingStrategy,
  ProviderPerformance 
} from '@/types';
import { PromptAnalyzer } from './prompt-analyzer';
import { getProviderCapabilities, getAllProviders, DEFAULT_ROUTING_STRATEGIES } from './provider-capabilities';

export class ProviderRouter {
  private performanceData: Map<AIProvider, ProviderPerformance> = new Map();
  private fallbackOrder: AIProvider[] = ['openai', 'anthropic', 'google', 'cohere', 'huggingface'];

  constructor() {
    this.initializePerformanceData();
  }

  private initializePerformanceData() {
    // Initialize with default performance data
    const providers = getAllProviders();
    providers.forEach(provider => {
      this.performanceData.set(provider, {
        provider,
        success_rate: 0.95, // Default 95% success rate
        average_response_time: this.getDefaultResponseTime(provider),
        average_cost: this.getDefaultCost(provider),
        quality_ratings: [8, 8, 8, 8, 8], // Default quality ratings
        last_updated: new Date().toISOString(),
        total_requests: 0,
        failed_requests: 0
      });
    });
  }

  private getDefaultResponseTime(provider: AIProvider): number {
    const times = {
      openai: 2500,
      anthropic: 3000,
      google: 1500,
      cohere: 2000,
      huggingface: 4000
    };
    return times[provider] || 3000;
  }

  private getDefaultCost(provider: AIProvider): number {
    const costs = {
      openai: 0.002,
      anthropic: 0.003,
      google: 0.001,
      cohere: 0.0015,
      huggingface: 0.0005
    };
    return costs[provider] || 0.002;
  }

  public async routeProvider(
    prompt: string, 
    strategy: RoutingStrategy = DEFAULT_ROUTING_STRATEGIES.balanced,
    excludeProviders: AIProvider[] = []
  ): Promise<RoutingDecision> {
    const startTime = Date.now();

    // Analyze the prompt
    const characteristics = PromptAnalyzer.analyze(prompt);

    // Get available providers
    const availableProviders = getAllProviders().filter(p => !excludeProviders.includes(p));

    // Score all providers
    const providerScores = await this.scoreProviders(characteristics, strategy, availableProviders);

    // Sort by score (descending)
    providerScores.sort((a, b) => b.score - a.score);

    const selectedProvider = providerScores[0];
    const alternatives = providerScores.slice(1);

    const analysisTime = Date.now() - startTime;

    return {
      selected_provider: selectedProvider.provider,
      score: selectedProvider.score,
      confidence: selectedProvider.confidence,
      reasoning: selectedProvider.reasoning,
      alternatives,
      routing_strategy: strategy,
      analysis_time_ms: analysisTime,
      prompt_characteristics: characteristics
    };
  }

  private async scoreProviders(
    characteristics: PromptCharacteristics,
    strategy: RoutingStrategy,
    availableProviders: AIProvider[]
  ): Promise<ProviderScore[]> {
    const scores: ProviderScore[] = [];

    for (const provider of availableProviders) {
      const score = await this.scoreProvider(provider, characteristics, strategy);
      scores.push(score);
    }

    return scores;
  }

  private async scoreProvider(
    provider: AIProvider,
    characteristics: PromptCharacteristics,
    strategy: RoutingStrategy
  ): Promise<ProviderScore> {
    const capabilities = getProviderCapabilities(provider);
    const performance = this.performanceData.get(provider)!;
    const reasoning: string[] = [];

    // Domain match score
    const domainScore = capabilities.domains[characteristics.domain] || 5;
    const domainWeight = strategy.weights.domain_match;
    reasoning.push(`Domain (${characteristics.domain}): ${domainScore}/10`);

    // Complexity match score
    const complexityScore = capabilities.complexity[characteristics.complexity] || 5;
    reasoning.push(`Complexity (${characteristics.complexity}): ${complexityScore}/10`);

    // Length match score
    const lengthScore = capabilities.length[characteristics.length] || 5;
    reasoning.push(`Length (${characteristics.length}): ${lengthScore}/10`);

    // Intent match score
    const intentScore = capabilities.intent[characteristics.intent] || 5;
    reasoning.push(`Intent (${characteristics.intent}): ${intentScore}/10`);

    // Language style match score
    const styleScore = capabilities.language_style[characteristics.language_style] || 5;
    reasoning.push(`Style (${characteristics.language_style}): ${styleScore}/10`);

    // Calculate weighted domain match score
    const domainMatchScore = (domainScore + complexityScore + lengthScore + intentScore + styleScore) / 5;

    // Quality score (from capabilities and performance)
    const qualityScore = (capabilities.quality + this.getAverageQuality(performance)) / 2;
    const qualityWeight = strategy.weights.quality;

    // Speed score (inverse of response time, normalized)
    const speedScore = Math.max(0, 10 - (performance.average_response_time / 1000));
    const speedWeight = strategy.weights.speed;

    // Cost score (inverse of cost, normalized)
    const costScore = Math.max(0, 10 - (performance.average_cost * 1000));
    const costWeight = strategy.weights.cost;

    // Reliability score
    const reliabilityScore = capabilities.reliability * performance.success_rate;
    const reliabilityWeight = strategy.weights.reliability;

    // Calculate final weighted score
    const finalScore = (
      domainMatchScore * domainWeight +
      qualityScore * qualityWeight +
      speedScore * speedWeight +
      costScore * costWeight +
      reliabilityScore * reliabilityWeight
    );

    // Apply thresholds
    let adjustedScore = finalScore;
    if (strategy.quality_threshold && qualityScore < strategy.quality_threshold) {
      adjustedScore *= 0.7; // Penalty for not meeting quality threshold
      reasoning.push(`Quality penalty applied (below ${strategy.quality_threshold})`);
    }

    if (strategy.cost_threshold && performance.average_cost > strategy.cost_threshold) {
      adjustedScore *= 0.8; // Penalty for exceeding cost threshold
      reasoning.push(`Cost penalty applied (above $${strategy.cost_threshold})`);
    }

    // Special handling for prompt characteristics
    if (characteristics.has_code && capabilities.specialties.includes('code_generation')) {
      adjustedScore *= 1.1;
      reasoning.push('Code generation bonus applied');
    }

    if (characteristics.safety_level === 'critical' && capabilities.safety >= 8) {
      adjustedScore *= 1.1;
      reasoning.push('Safety handling bonus applied');
    }

    if (characteristics.performance_requirement === 'speed' && speedScore >= 8) {
      adjustedScore *= 1.1;
      reasoning.push('Speed requirement bonus applied');
    }

    if (characteristics.performance_requirement === 'quality' && qualityScore >= 8) {
      adjustedScore *= 1.1;
      reasoning.push('Quality requirement bonus applied');
    }

    // Calculate confidence based on score distribution and provider reliability
    const confidence = Math.min(0.95, (adjustedScore / 10) * performance.success_rate);

    return {
      provider,
      score: Math.round(adjustedScore * 100) / 100,
      confidence: Math.round(confidence * 100) / 100,
      reasoning,
      cost_estimate: this.estimateCost(characteristics, performance),
      speed_estimate: performance.average_response_time,
      quality_estimate: qualityScore
    };
  }

  private getAverageQuality(performance: ProviderPerformance): number {
    if (performance.quality_ratings.length === 0) return 7; // Default
    return performance.quality_ratings.reduce((sum, rating) => sum + rating, 0) / performance.quality_ratings.length;
  }

  private estimateCost(characteristics: PromptCharacteristics, performance: ProviderPerformance): number {
    // Base cost adjusted for prompt length and complexity
    let multiplier = 1;
    
    if (characteristics.length === 'long') multiplier *= 1.5;
    if (characteristics.length === 'very_long') multiplier *= 2;
    if (characteristics.complexity === 'complex') multiplier *= 1.3;
    if (characteristics.complexity === 'very_complex') multiplier *= 1.6;

    return Math.round(performance.average_cost * multiplier * 10000) / 10000;
  }

  public async getFallbackProvider(
    excludeProviders: AIProvider[],
    characteristics: PromptCharacteristics,
    strategy: RoutingStrategy
  ): Promise<AIProvider | null> {
    const availableProviders = this.fallbackOrder.filter(p => !excludeProviders.includes(p));
    
    if (availableProviders.length === 0) return null;

    // For fallback, prioritize reliability and general capabilities
    const fallbackStrategy: RoutingStrategy = {
      ...strategy,
      weights: {
        quality: 0.2,
        speed: 0.1,
        cost: 0.1,
        reliability: 0.4,
        domain_match: 0.2
      }
    };

    const scores = await this.scoreProviders(characteristics, fallbackStrategy, availableProviders);
    scores.sort((a, b) => b.score - a.score);

    return scores[0]?.provider || null;
  }

  public updatePerformance(
    provider: AIProvider,
    responseTime: number,
    cost: number,
    success: boolean,
    qualityRating?: number
  ) {
    const current = this.performanceData.get(provider);
    if (!current) return;

    const updated: ProviderPerformance = {
      ...current,
      total_requests: current.total_requests + 1,
      failed_requests: success ? current.failed_requests : current.failed_requests + 1,
      average_response_time: (current.average_response_time + responseTime) / 2,
      average_cost: (current.average_cost + cost) / 2,
      success_rate: (current.total_requests - current.failed_requests) / current.total_requests,
      last_updated: new Date().toISOString()
    };

    if (qualityRating) {
      updated.quality_ratings = [...current.quality_ratings.slice(-4), qualityRating];
    }

    this.performanceData.set(provider, updated);
  }

  public getPerformanceData(): Map<AIProvider, ProviderPerformance> {
    return new Map(this.performanceData);
  }

  public getRoutingStrategies() {
    return DEFAULT_ROUTING_STRATEGIES;
  }

  public async testProvider(provider: AIProvider): Promise<boolean> {
    try {
      // This would normally make a test API call to the provider
      // For now, we'll simulate based on reliability score
      const performance = this.performanceData.get(provider);
      if (!performance) return false;

      return Math.random() < performance.success_rate;
    } catch (error) {
      return false;
    }
  }

  public getProviderRecommendation(characteristics: PromptCharacteristics): {
    recommended: AIProvider;
    reason: string;
  } {
    // Simple rule-based recommendations for common scenarios
    if (characteristics.has_code && characteristics.complexity !== 'simple') {
      return {
        recommended: 'openai',
        reason: 'OpenAI excels at code generation and complex programming tasks'
      };
    }

    if (characteristics.complexity === 'very_complex' || characteristics.length === 'very_long') {
      return {
        recommended: 'anthropic',
        reason: 'Claude handles complex reasoning and long contexts exceptionally well'
      };
    }

    if (characteristics.performance_requirement === 'speed') {
      return {
        recommended: 'google',
        reason: 'Gemini provides fast response times for quick tasks'
      };
    }

    if (characteristics.domain === 'creative') {
      return {
        recommended: 'openai',
        reason: 'GPT models are excellent for creative writing and ideation'
      };
    }

    if (characteristics.safety_level === 'critical') {
      return {
        recommended: 'anthropic',
        reason: 'Claude has superior safety handling for sensitive content'
      };
    }

    return {
      recommended: 'openai',
      reason: 'GPT models provide excellent general-purpose performance'
    };
  }
}

// Singleton instance for global use
export const providerRouter = new ProviderRouter();
