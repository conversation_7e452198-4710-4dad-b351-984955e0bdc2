import { AIProvider, ProviderCapabilities } from '@/types';

export const PROVIDER_CAPABILITIES: Record<AIProvider, ProviderCapabilities> = {
  openai: {
    domains: {
      general: 9,
      code: 9,
      creative: 8,
      business: 7,
      academic: 7,
      technical: 8,
      conversational: 9
    },
    complexity: {
      simple: 8,
      moderate: 9,
      complex: 8,
      very_complex: 7
    },
    length: {
      short: 8,
      medium: 9,
      long: 8,
      very_long: 7
    },
    intent: {
      generation: 9,
      analysis: 7,
      conversation: 9,
      instruction: 8,
      creative: 8,
      problem_solving: 8
    },
    language_style: {
      formal: 8,
      casual: 9,
      technical: 8,
      academic: 7,
      creative: 8
    },
    speed: 7,
    cost_efficiency: 6,
    reliability: 9,
    safety: 8,
    quality: 8,
    specialties: ['code_generation', 'creative_writing', 'general_purpose', 'function_calling']
  },

  anthropic: {
    domains: {
      general: 9,
      code: 8,
      creative: 9,
      business: 8,
      academic: 9,
      technical: 9,
      conversational: 8
    },
    complexity: {
      simple: 7,
      moderate: 9,
      complex: 9,
      very_complex: 9
    },
    length: {
      short: 7,
      medium: 8,
      long: 9,
      very_long: 9
    },
    intent: {
      generation: 8,
      analysis: 9,
      conversation: 8,
      instruction: 9,
      creative: 9,
      problem_solving: 9
    },
    language_style: {
      formal: 9,
      casual: 7,
      technical: 9,
      academic: 9,
      creative: 8
    },
    speed: 6,
    cost_efficiency: 5,
    reliability: 8,
    safety: 9,
    quality: 9,
    specialties: ['reasoning', 'analysis', 'safety', 'long_context', 'complex_instructions']
  },

  google: {
    domains: {
      general: 8,
      code: 7,
      creative: 6,
      business: 7,
      academic: 8,
      technical: 8,
      conversational: 7
    },
    complexity: {
      simple: 8,
      moderate: 8,
      complex: 7,
      very_complex: 6
    },
    length: {
      short: 9,
      medium: 8,
      long: 7,
      very_long: 6
    },
    intent: {
      generation: 7,
      analysis: 8,
      conversation: 7,
      instruction: 7,
      creative: 6,
      problem_solving: 7
    },
    language_style: {
      formal: 8,
      casual: 7,
      technical: 8,
      academic: 8,
      creative: 6
    },
    speed: 9,
    cost_efficiency: 8,
    reliability: 8,
    safety: 8,
    quality: 7,
    specialties: ['speed', 'factual_information', 'multimodal', 'search_integration']
  },

  cohere: {
    domains: {
      general: 7,
      code: 6,
      creative: 7,
      business: 8,
      academic: 7,
      technical: 7,
      conversational: 6
    },
    complexity: {
      simple: 7,
      moderate: 7,
      complex: 6,
      very_complex: 5
    },
    length: {
      short: 8,
      medium: 8,
      long: 7,
      very_long: 6
    },
    intent: {
      generation: 8,
      analysis: 6,
      conversation: 6,
      instruction: 7,
      creative: 7,
      problem_solving: 6
    },
    language_style: {
      formal: 8,
      casual: 6,
      technical: 7,
      academic: 7,
      creative: 7
    },
    speed: 8,
    cost_efficiency: 9,
    reliability: 7,
    safety: 7,
    quality: 6,
    specialties: ['text_generation', 'embeddings', 'classification', 'cost_effective']
  },

  huggingface: {
    domains: {
      general: 6,
      code: 7,
      creative: 6,
      business: 5,
      academic: 6,
      technical: 7,
      conversational: 5
    },
    complexity: {
      simple: 7,
      moderate: 6,
      complex: 5,
      very_complex: 4
    },
    length: {
      short: 7,
      medium: 6,
      long: 5,
      very_long: 4
    },
    intent: {
      generation: 6,
      analysis: 5,
      conversation: 5,
      instruction: 6,
      creative: 6,
      problem_solving: 6
    },
    language_style: {
      formal: 6,
      casual: 6,
      technical: 7,
      academic: 6,
      creative: 6
    },
    speed: 6,
    cost_efficiency: 10,
    reliability: 6,
    safety: 6,
    quality: 5,
    specialties: ['open_source', 'specialized_models', 'cost_effective', 'customizable']
  },

  auto: {
    domains: {
      general: 10,
      code: 10,
      creative: 10,
      business: 10,
      academic: 10,
      technical: 10,
      conversational: 10
    },
    complexity: {
      simple: 10,
      moderate: 10,
      complex: 10,
      very_complex: 10
    },
    length: {
      short: 10,
      medium: 10,
      long: 10,
      very_long: 10
    },
    intent: {
      generation: 10,
      analysis: 10,
      conversation: 10,
      instruction: 10,
      creative: 10,
      problem_solving: 10
    },
    language_style: {
      formal: 10,
      casual: 10,
      technical: 10,
      academic: 10,
      creative: 10
    },
    speed: 10,
    cost_efficiency: 10,
    reliability: 10,
    safety: 10,
    quality: 10,
    specialties: ['intelligent_routing', 'optimal_selection', 'adaptive']
  }
};

export const DEFAULT_ROUTING_STRATEGIES = {
  balanced: {
    name: 'Balanced',
    weights: {
      quality: 0.3,
      speed: 0.2,
      cost: 0.2,
      reliability: 0.15,
      domain_match: 0.15
    },
    fallback_enabled: true
  },
  quality_first: {
    name: 'Quality First',
    weights: {
      quality: 0.5,
      speed: 0.1,
      cost: 0.1,
      reliability: 0.15,
      domain_match: 0.15
    },
    fallback_enabled: true,
    quality_threshold: 7
  },
  cost_optimized: {
    name: 'Cost Optimized',
    weights: {
      quality: 0.2,
      speed: 0.2,
      cost: 0.4,
      reliability: 0.1,
      domain_match: 0.1
    },
    fallback_enabled: true,
    cost_threshold: 0.01
  },
  speed_optimized: {
    name: 'Speed Optimized',
    weights: {
      quality: 0.2,
      speed: 0.4,
      cost: 0.2,
      reliability: 0.1,
      domain_match: 0.1
    },
    fallback_enabled: true
  }
};

export function getProviderCapabilities(provider: AIProvider): ProviderCapabilities {
  return PROVIDER_CAPABILITIES[provider];
}

export function getAllProviders(): AIProvider[] {
  return Object.keys(PROVIDER_CAPABILITIES).filter(p => p !== 'auto') as AIProvider[];
}
