import { Template, TemplateCategory } from '@/types';

export const templateCategories: TemplateCategory[] = [
  {
    id: 'content-creation',
    name: 'Content Creation',
    description: 'Templates for blogs, social media, and marketing content',
    icon: '✍️'
  },
  {
    id: 'code-generation',
    name: 'Code Generation',
    description: 'Templates for documentation, code reviews, and debugging',
    icon: '💻'
  },
  {
    id: 'analysis-research',
    name: 'Analysis & Research',
    description: 'Templates for data analysis, summarization, and Q&A',
    icon: '📊'
  },
  {
    id: 'creative-writing',
    name: 'Creative Writing',
    description: 'Templates for stories, poetry, and creative content',
    icon: '🎨'
  },
  {
    id: 'business-communication',
    name: 'Business Communication',
    description: 'Templates for emails, proposals, and reports',
    icon: '💼'
  },
  {
    id: 'education-learning',
    name: 'Education & Learning',
    description: 'Templates for explanations, tutorials, and study materials',
    icon: '🎓'
  }
];

export const defaultTemplates: Template[] = [
  // Content Creation Templates
  {
    id: 'blog-post-outline',
    title: 'Blog Post Outline',
    description: 'Create a comprehensive outline for a blog post',
    category: 'content-creation',
    prompt: 'Create a detailed blog post outline about [TOPIC]. Include:\n- Compelling headline\n- Introduction hook\n- 5-7 main sections with subpoints\n- Key takeaways\n- Call-to-action\n\nTarget audience: [AUDIENCE]\nTone: [TONE]\nWord count goal: [WORD_COUNT]',
    variables: ['TOPIC', 'AUDIENCE', 'TONE', 'WORD_COUNT'],
    tags: ['blog', 'content', 'outline', 'writing'],
    difficulty: 'beginner',
    estimatedTokens: 150,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'social-media-campaign',
    title: 'Social Media Campaign',
    description: 'Generate a multi-platform social media campaign',
    category: 'content-creation',
    prompt: 'Create a 7-day social media campaign for [PRODUCT/SERVICE]. Include:\n- Platform-specific posts (Instagram, Twitter, LinkedIn, Facebook)\n- Engaging captions with hashtags\n- Content themes for each day\n- Visual content suggestions\n- Engagement strategies\n\nBrand voice: [BRAND_VOICE]\nTarget audience: [TARGET_AUDIENCE]\nCampaign goal: [GOAL]',
    variables: ['PRODUCT/SERVICE', 'BRAND_VOICE', 'TARGET_AUDIENCE', 'GOAL'],
    tags: ['social media', 'marketing', 'campaign', 'content'],
    difficulty: 'intermediate',
    estimatedTokens: 200,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  
  // Code Generation Templates
  {
    id: 'code-review-checklist',
    title: 'Code Review Checklist',
    description: 'Generate a comprehensive code review checklist',
    category: 'code-generation',
    prompt: 'Create a detailed code review checklist for [PROGRAMMING_LANGUAGE] projects. Include:\n- Code quality standards\n- Security considerations\n- Performance optimization points\n- Documentation requirements\n- Testing coverage\n- Best practices specific to [FRAMEWORK]\n\nProject type: [PROJECT_TYPE]\nTeam size: [TEAM_SIZE]',
    variables: ['PROGRAMMING_LANGUAGE', 'FRAMEWORK', 'PROJECT_TYPE', 'TEAM_SIZE'],
    tags: ['code review', 'quality', 'checklist', 'development'],
    difficulty: 'intermediate',
    estimatedTokens: 180,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'api-documentation',
    title: 'API Documentation',
    description: 'Generate comprehensive API documentation',
    category: 'code-generation',
    prompt: 'Create detailed API documentation for [API_NAME]. Include:\n- Overview and purpose\n- Authentication methods\n- Endpoint descriptions with HTTP methods\n- Request/response examples\n- Error codes and handling\n- Rate limiting information\n- SDK examples in [LANGUAGE]\n\nAPI type: [API_TYPE]\nVersion: [VERSION]',
    variables: ['API_NAME', 'LANGUAGE', 'API_TYPE', 'VERSION'],
    tags: ['api', 'documentation', 'development', 'reference'],
    difficulty: 'advanced',
    estimatedTokens: 250,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Analysis & Research Templates
  {
    id: 'data-analysis-report',
    title: 'Data Analysis Report',
    description: 'Create a comprehensive data analysis report',
    category: 'analysis-research',
    prompt: 'Analyze the following data and create a comprehensive report:\n\nData: [DATA_DESCRIPTION]\n\nInclude:\n- Executive summary\n- Key findings and insights\n- Statistical analysis\n- Trends and patterns\n- Recommendations\n- Visualizations suggestions\n- Limitations and assumptions\n\nAnalysis focus: [FOCUS_AREA]\nStakeholder audience: [AUDIENCE]',
    variables: ['DATA_DESCRIPTION', 'FOCUS_AREA', 'AUDIENCE'],
    tags: ['data analysis', 'report', 'insights', 'statistics'],
    difficulty: 'advanced',
    estimatedTokens: 220,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'competitive-analysis',
    title: 'Competitive Analysis',
    description: 'Conduct a thorough competitive analysis',
    category: 'analysis-research',
    prompt: 'Conduct a competitive analysis for [COMPANY/PRODUCT] in the [INDUSTRY] industry. Include:\n- Top 5 competitors identification\n- Strengths and weaknesses comparison\n- Market positioning analysis\n- Pricing strategy comparison\n- Feature comparison matrix\n- Market share insights\n- Opportunities and threats\n- Strategic recommendations\n\nFocus area: [FOCUS_AREA]\nTime frame: [TIME_FRAME]',
    variables: ['COMPANY/PRODUCT', 'INDUSTRY', 'FOCUS_AREA', 'TIME_FRAME'],
    tags: ['competitive analysis', 'market research', 'strategy', 'business'],
    difficulty: 'intermediate',
    estimatedTokens: 200,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Creative Writing Templates
  {
    id: 'short-story-prompt',
    title: 'Short Story Generator',
    description: 'Generate creative short story prompts and outlines',
    category: 'creative-writing',
    prompt: 'Create a compelling short story outline with the following elements:\n\nGenre: [GENRE]\nSetting: [SETTING]\nMain character: [CHARACTER_DESCRIPTION]\nConflict: [CONFLICT_TYPE]\n\nInclude:\n- Opening scene description\n- Character development arc\n- Plot progression (beginning, middle, end)\n- Key dialogue moments\n- Climax and resolution\n- Themes to explore\n\nTarget length: [WORD_COUNT] words\nTone: [TONE]',
    variables: ['GENRE', 'SETTING', 'CHARACTER_DESCRIPTION', 'CONFLICT_TYPE', 'WORD_COUNT', 'TONE'],
    tags: ['creative writing', 'story', 'fiction', 'narrative'],
    difficulty: 'intermediate',
    estimatedTokens: 180,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Business Communication Templates
  {
    id: 'project-proposal',
    title: 'Project Proposal',
    description: 'Create a professional project proposal',
    category: 'business-communication',
    prompt: 'Create a comprehensive project proposal for [PROJECT_NAME]. Include:\n- Executive summary\n- Project objectives and scope\n- Methodology and approach\n- Timeline and milestones\n- Resource requirements\n- Budget breakdown\n- Risk assessment\n- Expected outcomes and ROI\n- Next steps\n\nClient: [CLIENT_NAME]\nProject type: [PROJECT_TYPE]\nBudget range: [BUDGET_RANGE]\nTimeline: [TIMELINE]',
    variables: ['PROJECT_NAME', 'CLIENT_NAME', 'PROJECT_TYPE', 'BUDGET_RANGE', 'TIMELINE'],
    tags: ['proposal', 'business', 'project management', 'professional'],
    difficulty: 'advanced',
    estimatedTokens: 250,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Education & Learning Templates
  {
    id: 'lesson-plan',
    title: 'Educational Lesson Plan',
    description: 'Create a structured lesson plan for any subject',
    category: 'education-learning',
    prompt: 'Create a detailed lesson plan for teaching [SUBJECT/TOPIC]. Include:\n- Learning objectives\n- Prerequisites and prior knowledge\n- Lesson structure (introduction, main content, conclusion)\n- Activities and exercises\n- Assessment methods\n- Materials and resources needed\n- Differentiation strategies\n- Homework/follow-up activities\n\nGrade level: [GRADE_LEVEL]\nLesson duration: [DURATION]\nLearning style focus: [LEARNING_STYLE]',
    variables: ['SUBJECT/TOPIC', 'GRADE_LEVEL', 'DURATION', 'LEARNING_STYLE'],
    tags: ['education', 'lesson plan', 'teaching', 'learning'],
    difficulty: 'intermediate',
    estimatedTokens: 200,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Template utility functions
export function getTemplatesByCategory(categoryId: string): Template[] {
  return defaultTemplates.filter(template => template.category === categoryId);
}

export function getTemplateById(id: string): Template | undefined {
  return defaultTemplates.find(template => template.id === id);
}

export function searchTemplates(query: string): Template[] {
  const lowercaseQuery = query.toLowerCase();
  return defaultTemplates.filter(template => 
    template.title.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}

export function getPopularTemplates(limit: number = 6): Template[] {
  // For now, return the first few templates. In a real app, this would be based on usage stats
  return defaultTemplates.slice(0, limit);
}

export function processTemplateVariables(prompt: string, variables: Record<string, string>): string {
  let processedPrompt = prompt;
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `[${key}]`;
    processedPrompt = processedPrompt.replace(new RegExp(placeholder, 'g'), value);
  });
  return processedPrompt;
}
