'use client';

import { useState } from 'react';
import { EnhancementRule, RuleEvaluationResult } from '@/types';
import { RulesEngine } from '@/lib/rules-engine';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Play, Clock, CheckCircle, XCircle, Copy } from 'lucide-react';

interface RuleTesterProps {
  rule: EnhancementRule;
  onBack: () => void;
}

export function RuleTester({ rule, onBack }: RuleTesterProps) {
  const [testPrompt, setTestPrompt] = useState('');
  const [result, setResult] = useState<{
    enhancedPrompt: string;
    appliedRules: RuleEvaluationResult[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const samplePrompts = [
    'Write a blog post about AI',
    'Help me create a function that sorts an array',
    'Explain quantum computing',
    'Create a marketing strategy for a new product',
    'Tell me about machine learning algorithms',
    'Write a short story about space exploration',
    'How do I optimize database performance?',
    'Create a lesson plan for teaching Python'
  ];

  const handleTest = async () => {
    if (!testPrompt.trim()) return;

    setIsLoading(true);
    try {
      const engine = new RulesEngine([rule]);
      const testResult = await engine.applyRules(testPrompt);
      setResult(testResult);
    } catch (error) {
      console.error('Error testing rule:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyResult = () => {
    if (result?.enhancedPrompt) {
      navigator.clipboard.writeText(result.enhancedPrompt);
    }
  };

  const ruleResult = result?.appliedRules[0];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
          <Button 
            variant="ghost" 
            onClick={onBack} 
            className="self-start p-2 sm:p-3"
          >
            <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
          <div className="flex-1 min-w-0">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">
              Test Rule: {rule.name}
            </h2>
            <p className="text-sm sm:text-base text-gray-600 mt-1">
              {rule.description}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="space-y-6">
            {/* Test Prompt Input */}
            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Test Prompt</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Enter a prompt to test against this rule:
                  </label>
                  <textarea
                    value={testPrompt}
                    onChange={(e) => setTestPrompt(e.target.value)}
                    placeholder="Type your test prompt here..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                </div>

                <Button 
                  onClick={handleTest} 
                  disabled={!testPrompt.trim() || isLoading}
                  className="w-full sm:w-auto flex items-center justify-center gap-2 min-h-[44px]"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                      Testing...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      Test Rule
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Sample Prompts */}
            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Sample Prompts</h3>
              <p className="text-sm text-gray-600 mb-4">
                Click any sample prompt to test it quickly:
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {samplePrompts.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => setTestPrompt(prompt)}
                    className="text-left p-3 text-sm bg-gray-50 hover:bg-gray-100 rounded-md transition-colors border border-gray-200 min-h-[44px] flex items-center"
                  >
                    {prompt}
                  </button>
                ))}
              </div>
            </div>

            {/* Rule Details */}
            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Rule Details</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Conditions ({rule.conditions.length})</h4>
                  <div className="space-y-2">
                    {rule.conditions.map((condition, index) => (
                      <div key={condition.id} className="text-sm bg-gray-50 p-3 rounded border">
                        <div className="font-medium text-gray-700">
                          Condition {index + 1}
                        </div>
                        <div className="text-gray-600 mt-1">
                          {condition.field} {condition.type} {condition.operator} "{condition.value}"
                          {condition.caseSensitive && ' (case sensitive)'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Actions ({rule.actions.length})</h4>
                  <div className="space-y-2">
                    {rule.actions.map((action, index) => (
                      <div key={action.id} className="text-sm bg-gray-50 p-3 rounded border">
                        <div className="font-medium text-gray-700">
                          Action {index + 1}
                        </div>
                        <div className="text-gray-600 mt-1">
                          {action.type} at {action.target}
                        </div>
                        <div className="text-gray-500 mt-1 font-mono text-xs break-all">
                          "{action.content.substring(0, 100)}{action.content.length > 100 ? '...' : ''}"
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            {result ? (
              <>
                {/* Test Results */}
                <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
                    <h3 className="text-lg font-semibold">Test Results</h3>
                    <div className="flex items-center gap-2 text-sm">
                      {ruleResult?.applied ? (
                        <div className="flex items-center gap-1 text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          Rule Applied
                        </div>
                      ) : (
                        <div className="flex items-center gap-1 text-red-600">
                          <XCircle className="h-4 w-4" />
                          Rule Not Applied
                        </div>
                      )}
                      <div className="flex items-center gap-1 text-gray-500">
                        <Clock className="h-4 w-4" />
                        {ruleResult?.executionTime}ms
                      </div>
                    </div>
                  </div>

                  {ruleResult?.reason && (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-800">
                        <strong>Reason:</strong> {ruleResult.reason}
                      </p>
                    </div>
                  )}

                  {/* Before/After Comparison */}
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Original Prompt</h4>
                      <div className="p-3 bg-gray-50 rounded-md border text-sm">
                        <pre className="whitespace-pre-wrap font-sans break-words">
                          {testPrompt}
                        </pre>
                      </div>
                    </div>

                    <div>
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                        <h4 className="font-medium text-gray-900">Enhanced Prompt</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopyResult}
                          className="self-start sm:self-auto flex items-center gap-1 min-h-[36px]"
                        >
                          <Copy className="h-3 w-3" />
                          Copy
                        </Button>
                      </div>
                      <div className={`p-3 rounded-md border text-sm ${
                        ruleResult?.applied 
                          ? 'bg-green-50 border-green-200' 
                          : 'bg-gray-50 border-gray-200'
                      }`}>
                        <pre className="whitespace-pre-wrap font-sans break-words">
                          {result.enhancedPrompt}
                        </pre>
                      </div>
                    </div>
                  </div>

                  {/* Changes Highlight */}
                  {ruleResult?.applied && ruleResult.beforeText !== ruleResult.afterText && (
                    <div className="mt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Changes Made</h4>
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-sm">
                        <div className="space-y-2">
                          <div>
                            <span className="font-medium text-red-600">- Before:</span>
                            <div className="ml-4 text-gray-700 font-mono text-xs break-all">
                              {ruleResult.beforeText?.substring(0, 200)}
                              {(ruleResult.beforeText?.length || 0) > 200 ? '...' : ''}
                            </div>
                          </div>
                          <div>
                            <span className="font-medium text-green-600">+ After:</span>
                            <div className="ml-4 text-gray-700 font-mono text-xs break-all">
                              {ruleResult.afterText?.substring(0, 200)}
                              {(ruleResult.afterText?.length || 0) > 200 ? '...' : ''}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Performance Metrics */}
                <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
                  <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
                  
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-gray-50 rounded-md">
                      <div className="text-2xl font-bold text-blue-600">
                        {ruleResult?.executionTime}ms
                      </div>
                      <div className="text-xs text-gray-600">Execution Time</div>
                    </div>
                    
                    <div className="text-center p-3 bg-gray-50 rounded-md">
                      <div className="text-2xl font-bold text-green-600">
                        {rule.conditions.length}
                      </div>
                      <div className="text-xs text-gray-600">Conditions</div>
                    </div>
                    
                    <div className="text-center p-3 bg-gray-50 rounded-md">
                      <div className="text-2xl font-bold text-purple-600">
                        {rule.actions.length}
                      </div>
                      <div className="text-xs text-gray-600">Actions</div>
                    </div>
                    
                    <div className="text-center p-3 bg-gray-50 rounded-md">
                      <div className="text-2xl font-bold text-orange-600">
                        {result.enhancedPrompt.length - testPrompt.length}
                      </div>
                      <div className="text-xs text-gray-600">Chars Added</div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <Play className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test</h3>
                <p className="text-gray-600">
                  Enter a test prompt and click "Test Rule" to see how this rule performs.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
