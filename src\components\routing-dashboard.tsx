'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  Settings, 
  TrendingUp, 
  Clock, 
  DollarSign, 
  CheckCircle, 
  XCircle,
  ArrowLeft,
  RefreshCw
} from 'lucide-react';
import { 
  AIProvider, 
  ProviderPerformance, 
  RoutingStrategy,
  PromptCharacteristics 
} from '@/types';
import { providerRouter } from '@/lib/provider-router';
import { DEFAULT_ROUTING_STRATEGIES } from '@/lib/provider-capabilities';

interface RoutingDashboardProps {
  onClose: () => void;
}

export function RoutingDashboard({ onClose }: RoutingDashboardProps) {
  const [performanceData, setPerformanceData] = useState<Map<AIProvider, ProviderPerformance>>(new Map());
  const [selectedStrategy, setSelectedStrategy] = useState<string>('balanced');
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const loadPerformanceData = async () => {
    setIsRefreshing(true);
    try {
      const data = providerRouter.getPerformanceData();
      setPerformanceData(data);
    } catch (error) {
      console.error('Failed to load performance data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(4)}`;
  };

  const formatTime = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getProviderDisplayName = (provider: AIProvider) => {
    const names = {
      openai: 'OpenAI',
      anthropic: 'Anthropic',
      google: 'Google AI',
      cohere: 'Cohere',
      huggingface: 'Hugging Face',
      auto: 'Auto Select'
    };
    return names[provider] || provider;
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 0.95) return 'text-green-600';
    if (rate >= 0.90) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 8) return 'text-green-600';
    if (quality >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const providers = Array.from(performanceData.keys()).filter(p => p !== 'auto');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-full sm:h-[90vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 sm:p-6 border-b bg-gray-50">
          <div className="flex items-center gap-4 mb-4 sm:mb-0">
            <Button variant="ghost" onClick={onClose} className="p-2 min-h-[44px] min-w-[44px]">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h2 className="text-xl sm:text-2xl font-bold flex items-center gap-2">
                <BarChart3 className="h-6 w-6 text-blue-600" />
                Provider Routing Dashboard
              </h2>
              <p className="text-sm text-gray-600">
                Monitor AI provider performance and configure routing strategies
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={loadPerformanceData}
              disabled={isRefreshing}
              className="min-h-[44px]"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4 sm:p-6">
          <div className="space-y-6">
            {/* Routing Strategy Selection */}
            <div className="bg-white border rounded-lg p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Routing Strategy
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(DEFAULT_ROUTING_STRATEGIES).map(([key, strategy]) => (
                  <div
                    key={key}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedStrategy === key
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedStrategy(key)}
                  >
                    <h4 className="font-medium mb-2">{strategy.name}</h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      <div>Quality: {(strategy.weights.quality * 100).toFixed(0)}%</div>
                      <div>Speed: {(strategy.weights.speed * 100).toFixed(0)}%</div>
                      <div>Cost: {(strategy.weights.cost * 100).toFixed(0)}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Performance Overview */}
            <div className="bg-white border rounded-lg p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Provider Performance Overview
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full min-w-[600px]">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-2">Provider</th>
                      <th className="text-left py-3 px-2">Success Rate</th>
                      <th className="text-left py-3 px-2">Avg Response Time</th>
                      <th className="text-left py-3 px-2">Avg Cost</th>
                      <th className="text-left py-3 px-2">Quality Score</th>
                      <th className="text-left py-3 px-2">Total Requests</th>
                    </tr>
                  </thead>
                  <tbody>
                    {providers.map(provider => {
                      const perf = performanceData.get(provider);
                      if (!perf) return null;

                      const avgQuality = perf.quality_ratings.length > 0
                        ? perf.quality_ratings.reduce((sum, rating) => sum + rating, 0) / perf.quality_ratings.length
                        : 0;

                      return (
                        <tr key={provider} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-2 font-medium">
                            {getProviderDisplayName(provider)}
                          </td>
                          <td className="py-3 px-2">
                            <div className="flex items-center gap-2">
                              {perf.success_rate >= 0.95 ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-600" />
                              )}
                              <span className={getSuccessRateColor(perf.success_rate)}>
                                {formatPercentage(perf.success_rate)}
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-2">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-gray-400" />
                              {formatTime(perf.average_response_time)}
                            </div>
                          </td>
                          <td className="py-3 px-2">
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-gray-400" />
                              {formatCurrency(perf.average_cost)}
                            </div>
                          </td>
                          <td className="py-3 px-2">
                            <span className={getQualityColor(avgQuality)}>
                              {avgQuality.toFixed(1)}/10
                            </span>
                          </td>
                          <td className="py-3 px-2">
                            <div className="text-sm">
                              <div>{perf.total_requests} total</div>
                              <div className="text-gray-500">
                                {perf.failed_requests} failed
                              </div>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Provider Capabilities Matrix */}
            <div className="bg-white border rounded-lg p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Provider Capabilities Matrix</h3>
              <div className="text-sm text-gray-600 mb-4">
                Scores are based on provider strengths across different domains and characteristics.
              </div>
              <div className="overflow-x-auto">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 min-w-[600px]">
                  <div>
                    <h4 className="font-medium mb-3">Domain Expertise</h4>
                    <div className="space-y-2">
                      {['general', 'code', 'creative', 'business', 'academic', 'technical'].map(domain => (
                        <div key={domain} className="flex items-center justify-between">
                          <span className="capitalize text-sm">{domain}</span>
                          <div className="flex gap-1">
                            {providers.map(provider => {
                              const capabilities = require('@/lib/provider-capabilities').getProviderCapabilities(provider);
                              const score = capabilities.domains[domain] || 0;
                              return (
                                <div
                                  key={provider}
                                  className={`w-6 h-6 rounded text-xs flex items-center justify-center text-white ${
                                    score >= 8 ? 'bg-green-500' :
                                    score >= 6 ? 'bg-yellow-500' :
                                    'bg-red-500'
                                  }`}
                                  title={`${getProviderDisplayName(provider)}: ${score}/10`}
                                >
                                  {score}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-3">Performance Characteristics</h4>
                    <div className="space-y-2">
                      {[
                        { key: 'speed', label: 'Speed' },
                        { key: 'cost_efficiency', label: 'Cost Efficiency' },
                        { key: 'reliability', label: 'Reliability' },
                        { key: 'safety', label: 'Safety' },
                        { key: 'quality', label: 'Quality' }
                      ].map(({ key, label }) => (
                        <div key={key} className="flex items-center justify-between">
                          <span className="text-sm">{label}</span>
                          <div className="flex gap-1">
                            {providers.map(provider => {
                              const capabilities = require('@/lib/provider-capabilities').getProviderCapabilities(provider);
                              const score = capabilities[key as keyof typeof capabilities] as number || 0;
                              return (
                                <div
                                  key={provider}
                                  className={`w-6 h-6 rounded text-xs flex items-center justify-center text-white ${
                                    score >= 8 ? 'bg-green-500' :
                                    score >= 6 ? 'bg-yellow-500' :
                                    'bg-red-500'
                                  }`}
                                  title={`${getProviderDisplayName(provider)}: ${score}/10`}
                                >
                                  {score}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
