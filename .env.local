# Database Configuration - Development Placeholders
NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************.placeholder
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.placeholder
SUPABASE_PROJECT_ID=placeholder

# Authentication
NEXTAUTH_SECRET=development-secret-key-change-in-production
NEXTAUTH_URL=http://localhost:3001

# AI Provider API Keys - Development Placeholders
OPENAI_API_KEY=sk-placeholder-openai-key
ANTHROPIC_API_KEY=sk-ant-placeholder-anthropic-key
GOOGLE_AI_API_KEY=placeholder-google-ai-key
COHERE_API_KEY=placeholder-cohere-key
HUGGINGFACE_API_KEY=hf_placeholder-huggingface-key

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=placeholder-google-client-id
GOOGLE_CLIENT_SECRET=placeholder-google-client-secret
GITHUB_CLIENT_ID=placeholder-github-client-id
GITHUB_CLIENT_SECRET=placeholder-github-client-secret

# Redis Configuration (Optional - uses memory cache if not provided)
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your_redis_password

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_FAILED_REQUESTS=true

# Application Configuration
NODE_ENV=development
APP_URL=http://localhost:3001
API_BASE_URL=http://localhost:3001/api

# Analytics & Monitoring (Optional)
ANALYTICS_ENABLED=false
# SENTRY_DSN=your_sentry_dsn
# POSTHOG_KEY=your_posthog_key

# Email Configuration (Optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your_email_password
# FROM_EMAIL=<EMAIL>

# Stripe Configuration (For payments)
# STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
# STRIPE_SECRET_KEY=your_stripe_secret_key
# STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Feature Flags
ENABLE_TEMPLATE_LIBRARY=true
ENABLE_CUSTOM_RULES=true
ENABLE_ANALYTICS_DASHBOARD=true
ENABLE_MULTI_PROVIDER=true

# Performance Configuration
CACHE_TTL_SECONDS=3600
MAX_PROMPT_LENGTH=10000
MAX_ENHANCEMENT_RETRIES=3
ENHANCEMENT_TIMEOUT_MS=30000

# Security Configuration
CORS_ORIGIN=http://localhost:3001
JWT_EXPIRY=24h
SESSION_MAX_AGE=86400
BCRYPT_ROUNDS=12

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=true

# Development Configuration
NEXT_TELEMETRY_DISABLED=1
DISABLE_ESLINT=false
ANALYZE_BUNDLE=false
