// Core Types for QueryBoost Application

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  plan: 'free' | 'pro' | 'enterprise';
  usage: UserUsage;
  preferences: UserPreferences;
  created_at: string;
  updated_at: string;
}

export interface UserUsage {
  monthly_enhancements: number;
  total_enhancements: number;
  last_reset: string;
  plan_limits: {
    max_monthly_enhancements: number;
    max_concurrent_requests: number;
    max_prompt_length: number;
  };
}

export interface UserPreferences {
  default_provider: AIProvider;
  default_enhancement_level: EnhancementLevel;
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    browser: boolean;
    usage_alerts: boolean;
  };
}

export type AIProvider = 'openai' | 'anthropic' | 'google' | 'cohere' | 'huggingface' | 'auto';

export type EnhancementLevel = 'conservative' | 'moderate' | 'aggressive';

// Template System Types
export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface Template {
  id: string;
  title: string;
  description: string;
  category: string;
  prompt: string;
  variables: string[];
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTokens: number;
  isPublic: boolean;
  userId?: string;
  usageCount?: number;
  rating?: number;
  createdAt: string;
  updatedAt: string;
}

// Enhancement Rules System Types
export interface EnhancementRule {
  id: string;
  name: string;
  description: string;
  category: string;
  priority: number; // Higher number = higher priority
  enabled: boolean;
  conditions: RuleCondition[];
  actions: RuleAction[];
  metadata: RuleMetadata;
  userId?: string;
  isPublic: boolean;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface RuleCondition {
  id: string;
  type: 'contains' | 'length' | 'domain' | 'regex' | 'starts_with' | 'ends_with' | 'word_count' | 'complexity';
  field: 'prompt' | 'enhanced_prompt' | 'metadata';
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'matches' | 'not_contains' | 'between';
  value: string | number;
  secondaryValue?: string | number; // For 'between' operator
  caseSensitive?: boolean;
  negate?: boolean; // Invert the condition result
}

export interface RuleAction {
  id: string;
  type: 'prepend' | 'append' | 'replace' | 'format' | 'enhance_section' | 'wrap' | 'insert_at';
  target: 'beginning' | 'end' | 'match' | 'entire' | 'position';
  content: string;
  position?: number;
  formatting?: {
    style: 'bold' | 'italic' | 'code' | 'quote' | 'list' | 'heading';
    level?: number; // For headings
  };
}

export interface RuleMetadata {
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedImpact: 'low' | 'medium' | 'high';
  compatibleProviders: AIProvider[];
  language?: string;
  domain?: string;
}

export interface RuleEvaluationResult {
  ruleId: string;
  ruleName: string;
  applied: boolean;
  reason?: string;
  beforeText?: string;
  afterText?: string;
  executionTime: number;
}

export interface RuleSet {
  id: string;
  name: string;
  description: string;
  rules: EnhancementRule[];
  version: string;
  author: string;
  createdAt: string;
  updatedAt: string;
}

export type RuleCategory =
  | 'content-enhancement'
  | 'formatting'
  | 'domain-specific'
  | 'length-optimization'
  | 'clarity-improvement'
  | 'context-addition'
  | 'style-adjustment'
  | 'custom';

export interface PromptEnhancement {
  id: string;
  user_id: string;
  original_prompt: string;
  enhanced_prompt: string;
  enhancement_score: number;
  provider_used: AIProvider;
  model_used: string;
  tokens_estimated: number;
  cost_estimated: number;
  enhancement_rules: EnhancementRule[];
  metadata: PromptMetadata;
  created_at: string;
}

export interface EnhancementRule {
  id: string;
  name: string;
  description: string;
  type: 'structure' | 'clarity' | 'specificity' | 'context' | 'format';
  priority: number;
  enabled: boolean;
  conditions: RuleCondition[];
  transformations: RuleTransformation[];
}

export interface RuleCondition {
  field: string;
  operator: 'contains' | 'equals' | 'starts_with' | 'ends_with' | 'regex';
  value: string;
}

export interface RuleTransformation {
  type: 'prepend' | 'append' | 'replace' | 'wrap' | 'format';
  value: string;
  position?: number;
}

export interface PromptMetadata {
  language: string;
  category: string;
  complexity: 'low' | 'medium' | 'high';
  intent: string;
  keywords: string[];
  estimated_response_length: number;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  template_content: string;
  variables: TemplateVariable[];
  usage_count: number;
  rating: number;
  is_public: boolean;
  created_by: string;
  created_at: string;
  tags: string[];
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean';
  description: string;
  required: boolean;
  default_value?: string;
  options?: string[];
  validation?: {
    min_length?: number;
    max_length?: number;
    pattern?: string;
  };
}

export interface EnhancementRequest {
  prompt: string;
  provider?: AIProvider;
  model?: string;
  enhancement_level?: EnhancementLevel;
  template_id?: string;
  custom_rules?: EnhancementRule[];
  metadata?: Partial<PromptMetadata>;
}

export interface EnhancementResponse {
  id: string;
  original_prompt: string;
  enhanced_prompt: string;
  enhancement_score: number;
  improvements: string[];
  estimated_tokens: number;
  estimated_cost: number;
  recommended_provider: AIProvider;
  processing_time_ms: number;
  suggestions: EnhancementSuggestion[];
}

export interface EnhancementSuggestion {
  type: 'improvement' | 'warning' | 'optimization';
  message: string;
  impact: 'low' | 'medium' | 'high';
  category: string;
}

export interface AnalyticsData {
  usage: {
    current_month: number;
    total_enhancements: number;
    average_score: number;
    success_rate: number;
  };
  trends: {
    daily_usage: Array<{ date: string; count: number }>;
    provider_distribution: Record<AIProvider, number>;
    category_breakdown: Record<string, number>;
    score_trends: Array<{ date: string; average_score: number }>;
  };
  cost_analysis: {
    total_spent: number;
    average_per_enhancement: number;
    savings_vs_direct: number;
    monthly_breakdown: Array<{ month: string; cost: number }>;
  };
  performance: {
    average_processing_time: number;
    success_rate: number;
    error_rate: number;
    cache_hit_rate: number;
  };
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    has_more?: boolean;
  };
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retry_after?: number;
}

export interface AIProviderConfig {
  name: string;
  display_name: string;
  models: AIModel[];
  pricing: ProviderPricing;
  capabilities: ProviderCapabilities;
  rate_limits: ProviderRateLimits;
  status: 'active' | 'maintenance' | 'deprecated';
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
  max_tokens: number;
  input_cost_per_token: number;
  output_cost_per_token: number;
  supports_streaming: boolean;
  context_window: number;
}

export interface ProviderPricing {
  currency: string;
  billing_unit: 'token' | 'request' | 'character';
  tiers: PricingTier[];
}

export interface PricingTier {
  name: string;
  min_usage: number;
  max_usage?: number;
  rate: number;
}

export interface ProviderCapabilities {
  supports_streaming: boolean;
  supports_functions: boolean;
  supports_vision: boolean;
  supports_code: boolean;
  max_prompt_length: number;
  languages: string[];
}

export interface ProviderRateLimits {
  requests_per_minute: number;
  tokens_per_minute: number;
  concurrent_requests: number;
}

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  expires_at: number;
  created_at: number;
  hit_count: number;
}

export interface WebSocketMessage {
  type: 'enhancement_progress' | 'enhancement_complete' | 'error' | 'ping' | 'pong';
  data: any;
  timestamp: number;
  request_id?: string;
}

export interface EnhancementProgress {
  stage: 'analyzing' | 'enhancing' | 'validating' | 'complete';
  progress: number;
  message: string;
  estimated_time_remaining?: number;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Form types
export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirm_password: string;
  terms_accepted: boolean;
}

export interface EnhancementForm {
  prompt: string;
  provider: AIProvider;
  model?: string;
  enhancement_level: EnhancementLevel;
  use_template?: boolean;
  template_id?: string;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  status: number;
  details?: any;
  timestamp: string;
}

export type ErrorCode = 
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'NOT_FOUND'
  | 'RATE_LIMITED'
  | 'INVALID_INPUT'
  | 'PROVIDER_ERROR'
  | 'ENHANCEMENT_FAILED'
  | 'QUOTA_EXCEEDED'
  | 'INTERNAL_ERROR';

// Event types for analytics
export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  user_id?: string;
  timestamp: string;
}

// AI Provider Routing types
export interface PromptCharacteristics {
  length: 'short' | 'medium' | 'long' | 'very_long';
  complexity: 'simple' | 'moderate' | 'complex' | 'very_complex';
  domain: 'general' | 'code' | 'creative' | 'business' | 'academic' | 'technical' | 'conversational';
  intent: 'generation' | 'analysis' | 'conversation' | 'instruction' | 'creative' | 'problem_solving';
  language_style: 'formal' | 'casual' | 'technical' | 'academic' | 'creative';
  safety_level: 'low' | 'medium' | 'high' | 'critical';
  performance_requirement: 'speed' | 'quality' | 'balanced';
  word_count: number;
  sentence_count: number;
  has_code: boolean;
  has_math: boolean;
  has_urls: boolean;
  has_sensitive_content: boolean;
}

export interface ProviderCapabilities {
  domains: Record<string, number>; // domain -> score (0-10)
  complexity: Record<string, number>; // complexity level -> score
  length: Record<string, number>; // length category -> score
  intent: Record<string, number>; // intent type -> score
  language_style: Record<string, number>; // style -> score
  speed: number; // processing speed score (0-10)
  cost_efficiency: number; // cost efficiency score (0-10)
  reliability: number; // uptime/availability score (0-10)
  safety: number; // content safety handling (0-10)
  quality: number; // overall quality score (0-10)
  specialties: string[]; // special capabilities
}

export interface ProviderScore {
  provider: AIProvider;
  score: number;
  confidence: number;
  reasoning: string[];
  cost_estimate: number;
  speed_estimate: number;
  quality_estimate: number;
}

export interface RoutingDecision {
  selected_provider: AIProvider;
  score: number;
  confidence: number;
  reasoning: string[];
  alternatives: ProviderScore[];
  routing_strategy: RoutingStrategy;
  analysis_time_ms: number;
  prompt_characteristics: PromptCharacteristics;
}

export interface RoutingStrategy {
  name: string;
  weights: {
    quality: number;
    speed: number;
    cost: number;
    reliability: number;
    domain_match: number;
  };
  fallback_enabled: boolean;
  cost_threshold?: number;
  quality_threshold?: number;
}

export interface ProviderPerformance {
  provider: AIProvider;
  success_rate: number;
  average_response_time: number;
  average_cost: number;
  quality_ratings: number[];
  last_updated: string;
  total_requests: number;
  failed_requests: number;
}
