'use client';

import { useState, useCallback } from 'react';
import { useAuth } from '@/components/providers';
import { Button } from '@/components/ui/button';
import { Loader2, <PERSON><PERSON><PERSON>, <PERSON>py, Book<PERSON>pen, Edit3, <PERSON><PERSON><PERSON>, Cog, BarChart3, Zap } from 'lucide-react';
import { EnhancementRequest, EnhancementResponse, AIProvider, EnhancementLevel, Template, EnhancementRule, RuleEvaluationResult, RoutingDecision } from '@/types';
import { TemplateLibrary } from '@/components/template-library';
import { TemplateEditor } from '@/components/template-editor';
import { RulesManager } from '@/components/rules-manager';
import { RuleEditor } from '@/components/rule-editor';
import { RuleTester } from '@/components/rule-tester';
import { RoutingDashboard } from '@/components/routing-dashboard';
import { RulesEngine } from '@/lib/rules-engine';
import { defaultRules, getEnabledRules } from '@/lib/default-rules';
import { providerRouter } from '@/lib/provider-router';
import { DEFAULT_ROUTING_STRATEGIES } from '@/lib/provider-capabilities';
// Simple toast function for now
const toast = (options: { title: string; description: string; variant?: 'destructive' }) => {
  alert(`${options.title}: ${options.description}`);
};

export function PromptEnhancer() {
  const { user, userProfile } = useAuth();
  const [prompt, setPrompt] = useState('');
  const [provider, setProvider] = useState<AIProvider>('auto');
  const [enhancementLevel, setEnhancementLevel] = useState<EnhancementLevel>('moderate');
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementResponse, setEnhancementResponse] = useState<EnhancementResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);

  // Rules management state
  const [showRulesManager, setShowRulesManager] = useState(false);
  const [showRuleEditor, setShowRuleEditor] = useState(false);
  const [showRuleTester, setShowRuleTester] = useState(false);
  const [selectedRule, setSelectedRule] = useState<EnhancementRule | null>(null);
  const [appliedRules, setAppliedRules] = useState<RuleEvaluationResult[]>([]);
  const [rules, setRules] = useState<EnhancementRule[]>(defaultRules);

  // Routing state
  const [showRoutingDashboard, setShowRoutingDashboard] = useState(false);
  const [routingDecision, setRoutingDecision] = useState<RoutingDecision | null>(null);
  const [routingStrategy, setRoutingStrategy] = useState('balanced');

  const providers = [
    { value: 'auto', label: 'Auto Select', description: 'Let QueryBoost choose the best provider' },
    { value: 'openai', label: 'OpenAI', description: 'GPT models for general tasks' },
    { value: 'anthropic', label: 'Anthropic', description: 'Claude for thoughtful responses' },
    { value: 'google', label: 'Google AI', description: 'Gemini for fast processing' },
    { value: 'cohere', label: 'Cohere', description: 'Specialized language models' },
    { value: 'huggingface', label: 'Hugging Face', description: 'Open source models' }
  ];

  const enhancementLevels = [
    { value: 'conservative', label: 'Conservative', description: 'Minimal changes, preserve original intent' },
    { value: 'moderate', label: 'Moderate', description: 'Balanced enhancement with good improvements' },
    { value: 'aggressive', label: 'Aggressive', description: 'Maximum optimization for best results' }
  ];

  const simulateProgress = useCallback(() => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
    return interval;
  }, []);

  const enhancePrompt = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a prompt to enhance',
        variant: 'destructive'
      });
      return;
    }

    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to enhance prompts',
        variant: 'destructive'
      });
      return;
    }

    setIsEnhancing(true);
    setError(null);
    setEnhancementResponse(null);
    setAppliedRules([]);

    const progressInterval = simulateProgress();

    try {
      // Step 1: Apply custom rules first
      const enabledRules = getEnabledRules();
      const rulesEngine = new RulesEngine(enabledRules);
      const rulesResult = await rulesEngine.applyRules(prompt.trim());

      setAppliedRules(rulesResult.appliedRules);

      // Step 2: Intelligent provider routing (if auto is selected)
      let selectedProvider = provider;
      let routing: RoutingDecision | null = null;

      if (provider === 'auto') {
        const strategy = DEFAULT_ROUTING_STRATEGIES[routingStrategy as keyof typeof DEFAULT_ROUTING_STRATEGIES];
        routing = await providerRouter.routeProvider(rulesResult.enhancedPrompt, strategy);
        selectedProvider = routing.selected_provider;
        setRoutingDecision(routing);
      }

      // Step 3: Use rules-enhanced prompt for API enhancement
      const request: EnhancementRequest = {
        prompt: rulesResult.enhancedPrompt,
        provider: selectedProvider === 'auto' ? undefined : selectedProvider,
        enhancement_level: enhancementLevel
      };

      const response = await fetch('/api/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || 'Enhancement failed');
      }

      setProgress(100);

      // Enhance the response with rules information
      const enhancedResponse = {
        ...data.data,
        original_prompt: prompt.trim(),
        rules_applied: rulesResult.appliedRules.filter(r => r.applied).length,
        rules_enhanced_prompt: rulesResult.enhancedPrompt
      };

      setEnhancementResponse(enhancedResponse);

      const appliedRulesCount = rulesResult.appliedRules.filter(r => r.applied).length;
      toast({
        title: 'Enhancement Complete!',
        description: `Your prompt has been enhanced with a score of ${data.data.enhancement_score}/10${appliedRulesCount > 0 ? ` (${appliedRulesCount} custom rules applied)` : ''}`,
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      toast({
        title: 'Enhancement Failed',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      clearInterval(progressInterval);
      setIsEnhancing(false);
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: 'Copied!',
        description: 'Text copied to clipboard',
      });
    } catch (err) {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy text to clipboard',
        variant: 'destructive'
      });
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 8) return 'default';
    if (score >= 6) return 'secondary';
    return 'destructive';
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    setShowTemplateLibrary(false);
    if (template.variables.length > 0) {
      setShowTemplateEditor(true);
    } else {
      setPrompt(template.prompt);
    }
  };

  const handleTemplateComplete = (processedPrompt: string) => {
    setPrompt(processedPrompt);
    setShowTemplateEditor(false);
    setSelectedTemplate(null);
  };

  // Rules management handlers
  const handleCreateRule = () => {
    setSelectedRule(null);
    setShowRulesManager(false);
    setShowRuleEditor(true);
  };

  const handleEditRule = (rule: EnhancementRule) => {
    setSelectedRule(rule);
    setShowRulesManager(false);
    setShowRuleEditor(true);
  };

  const handleTestRule = (rule: EnhancementRule) => {
    setSelectedRule(rule);
    setShowRulesManager(false);
    setShowRuleTester(true);
  };

  const handleSaveRule = (rule: EnhancementRule) => {
    setRules(prev => {
      const existingIndex = prev.findIndex(r => r.id === rule.id);
      if (existingIndex >= 0) {
        // Update existing rule
        const updated = [...prev];
        updated[existingIndex] = rule;
        return updated;
      } else {
        // Add new rule
        return [...prev, rule];
      }
    });
    setShowRuleEditor(false);
    setSelectedRule(null);
    setShowRulesManager(true);
  };

  const handleBackToRulesManager = () => {
    setShowRuleEditor(false);
    setShowRuleTester(false);
    setSelectedRule(null);
    setShowRulesManager(true);
  };

  const handleTemplateEditorBack = () => {
    setShowTemplateEditor(false);
    setShowTemplateLibrary(true);
  };

  return (
    <div className="space-y-6">
      {/* Input Section */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-600" />
            Prompt Enhancement
          </h3>
          <p className="card-description">
            Enter your prompt below and let QueryBoost optimize it for better AI responses
          </p>
        </div>
        <div className="card-content space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label htmlFor="prompt" className="text-sm font-medium">
                Your Prompt
              </label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTemplateLibrary(true)}
                  className="flex items-center gap-2"
                >
                  <BookOpen className="h-4 w-4" />
                  <span className="hidden sm:inline">Browse Templates</span>
                  <span className="sm:hidden">Templates</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowRulesManager(true)}
                  className="flex items-center gap-2"
                >
                  <Cog className="h-4 w-4" />
                  <span className="hidden sm:inline">Manage Rules</span>
                  <span className="sm:hidden">Rules</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowRoutingDashboard(true)}
                  className="flex items-center gap-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Routing</span>
                  <span className="sm:hidden">Route</span>
                </Button>
              </div>
            </div>
            <textarea
              id="prompt"
              placeholder="Enter your prompt here or browse templates to get started..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="textarea min-h-[120px] resize-none"
              disabled={isEnhancing}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{prompt.length} characters</span>
              <span>Max: 50,000 characters</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">AI Provider</label>
              <select
                value={provider}
                onChange={(e) => setProvider(e.target.value as AIProvider)}
                className="input"
              >
                {providers.map((p) => (
                  <option key={p.value} value={p.value}>
                    {p.label} - {p.description}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Enhancement Level</label>
              <select
                value={enhancementLevel}
                onChange={(e) => setEnhancementLevel(e.target.value as EnhancementLevel)}
                className="input"
              >
                {enhancementLevels.map((level) => (
                  <option key={level.value} value={level.value}>
                    {level.label} - {level.description}
                  </option>
                ))}
              </select>
            </div>

            {provider === 'auto' && (
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Routing Strategy
                </label>
                <select
                  value={routingStrategy}
                  onChange={(e) => setRoutingStrategy(e.target.value)}
                  className="input"
                >
                  {Object.entries(DEFAULT_ROUTING_STRATEGIES).map(([key, strategy]) => (
                    <option key={key} value={key}>
                      {strategy.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>

          {isEnhancing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Enhancing your prompt...</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          <Button
            onClick={enhancePrompt}
            disabled={isEnhancing || !prompt.trim()}
            className="w-full btn-primary"
          >
            {isEnhancing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Enhancing...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Enhance Prompt
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="alert alert-destructive">
          <p>{error}</p>
        </div>
      )}

      {/* Results Section */}
      {enhancementResponse && (
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="card-title">Enhancement Results</h3>
              <div className="flex items-center gap-2">
                <span className={`badge ${getScoreBadgeVariant(enhancementResponse.enhancement_score)}`}>
                  Score: {enhancementResponse.enhancement_score}/10
                </span>
                <span className="badge badge-outline">
                  {enhancementResponse.recommended_provider}
                </span>
              </div>
            </div>
            <p className="card-description">
              Processing time: {enhancementResponse.processing_time_ms}ms |
              Estimated tokens: {enhancementResponse.estimated_tokens} |
              Estimated cost: ${enhancementResponse.estimated_cost.toFixed(4)}
              {routingDecision && (
                <span> | Provider: {routingDecision.selected_provider} (Score: {routingDecision.score.toFixed(1)})</span>
              )}
            </p>
          </div>
          <div className="card-content">
            <div className="w-full">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Enhanced Prompt</h4>
                    <Button
                      variant="outline"
                      onClick={() => copyToClipboard(enhancementResponse.enhanced_prompt)}
                      className="btn-outline"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="whitespace-pre-wrap">{enhancementResponse.enhanced_prompt}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Original Prompt</h4>
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="whitespace-pre-wrap">{enhancementResponse.original_prompt}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Improvements Made</h4>
                    <ul className="space-y-1">
                      {enhancementResponse.improvements.map((improvement, index) => (
                        <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                          <span className="text-green-600 mt-1">•</span>
                          {improvement}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Applied Rules Section */}
                  {appliedRules.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Custom Rules Applied ({appliedRules.filter(r => r.applied).length}/{appliedRules.length})
                      </h4>
                      <div className="space-y-2">
                        {appliedRules.map((ruleResult, index) => (
                          <div
                            key={index}
                            className={`p-3 rounded-lg border ${
                              ruleResult.applied
                                ? 'bg-green-50 border-green-200'
                                : 'bg-gray-50 border-gray-200'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className={`w-2 h-2 rounded-full ${
                                  ruleResult.applied ? 'bg-green-500' : 'bg-gray-400'
                                }`} />
                                <span className="font-medium text-sm">{ruleResult.ruleName}</span>
                              </div>
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <span>{ruleResult.executionTime}ms</span>
                                {ruleResult.applied ? (
                                  <span className="text-green-600 font-medium">Applied</span>
                                ) : (
                                  <span className="text-gray-500">Skipped</span>
                                )}
                              </div>
                            </div>
                            {ruleResult.reason && (
                              <p className="text-xs text-gray-600">{ruleResult.reason}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Routing Decision Section */}
                  {routingDecision && (
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Intelligent Routing Decision
                      </h4>
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                          <div className="text-center">
                            <div className="text-lg font-semibold text-blue-600">
                              {routingDecision.selected_provider.toUpperCase()}
                            </div>
                            <div className="text-xs text-gray-600">Selected Provider</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-green-600">
                              {routingDecision.score.toFixed(1)}/10
                            </div>
                            <div className="text-xs text-gray-600">Routing Score</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-purple-600">
                              {(routingDecision.confidence * 100).toFixed(0)}%
                            </div>
                            <div className="text-xs text-gray-600">Confidence</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-orange-600">
                              {routingDecision.analysis_time_ms}ms
                            </div>
                            <div className="text-xs text-gray-600">Analysis Time</div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm font-medium">Reasoning:</div>
                          <ul className="text-sm text-gray-700 space-y-1">
                            {routingDecision.reasoning.map((reason, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <span className="text-blue-500 mt-1">•</span>
                                {reason}
                              </li>
                            ))}
                          </ul>
                        </div>
                        {routingDecision.alternatives.length > 0 && (
                          <div className="mt-3 pt-3 border-t border-blue-300">
                            <div className="text-sm font-medium mb-2">Alternative Providers:</div>
                            <div className="flex flex-wrap gap-2">
                              {routingDecision.alternatives.slice(0, 3).map((alt, index) => (
                                <div key={index} className="text-xs bg-white px-2 py-1 rounded border">
                                  {alt.provider.toUpperCase()} ({alt.score.toFixed(1)})
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {enhancementResponse.suggestions.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Suggestions</h4>
                      <div className="space-y-2">
                        {enhancementResponse.suggestions.map((suggestion, index) => (
                          <div key={index} className="p-3 bg-muted rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="badge badge-outline text-xs">
                                {suggestion.type}
                              </span>
                              <span className="badge badge-outline text-xs">
                                {suggestion.impact} impact
                              </span>
                            </div>
                            <p className="text-sm">{suggestion.message}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Template Library Modal */}
      {showTemplateLibrary && (
        <TemplateLibrary
          onSelectTemplate={handleTemplateSelect}
          onClose={() => setShowTemplateLibrary(false)}
        />
      )}

      {/* Template Editor Modal */}
      {showTemplateEditor && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-[80vh] overflow-y-auto">
            <TemplateEditor
              template={selectedTemplate}
              onComplete={handleTemplateComplete}
              onBack={handleTemplateEditorBack}
            />
          </div>
        </div>
      )}

      {/* Rules Manager Modal */}
      {showRulesManager && (
        <RulesManager
          onCreateRule={handleCreateRule}
          onEditRule={handleEditRule}
          onTestRule={handleTestRule}
          onClose={() => setShowRulesManager(false)}
        />
      )}

      {/* Rule Editor Modal */}
      {showRuleEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full sm:h-[90vh] overflow-y-auto">
            <RuleEditor
              rule={selectedRule || undefined}
              onSave={handleSaveRule}
              onBack={handleBackToRulesManager}
              onTest={handleTestRule}
            />
          </div>
        </div>
      )}

      {/* Rule Tester Modal */}
      {showRuleTester && selectedRule && (
        <RuleTester
          rule={selectedRule}
          onBack={handleBackToRulesManager}
        />
      )}

      {/* Routing Dashboard Modal */}
      {showRoutingDashboard && (
        <RoutingDashboard
          onClose={() => setShowRoutingDashboard(false)}
        />
      )}
    </div>
  );
}
