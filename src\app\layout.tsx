import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'QueryBoost - AI Prompt Enhancement Middleware',
  description: 'Transform basic prompts into optimized AI queries with intelligent middleware. Enhance your AI interactions with QueryBoost.',
  keywords: [
    'AI',
    'prompt engineering',
    'optimization',
    'middleware',
    'OpenAI',
    'Anthropic',
    'Google AI',
    'prompt enhancement',
    'artificial intelligence'
  ],
  authors: [{ name: 'QueryBoost Team' }],
  creator: 'QueryBoost',
  publisher: 'QueryBoost',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'QueryBoost - AI Prompt Enhancement Middleware',
    description: 'Transform basic prompts into optimized AI queries with intelligent middleware',
    siteName: 'QueryBoost',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'QueryBoost - AI Prompt Enhancement',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'QueryBoost - AI Prompt Enhancement Middleware',
    description: 'Transform basic prompts into optimized AI queries with intelligent middleware',
    images: ['/og-image.png'],
    creator: '@queryboost',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="msapplication-TileColor" content="#0ea5e9" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">
              {children}
            </div>
          </div>
        </Providers>
      </body>
    </html>
  );
}
